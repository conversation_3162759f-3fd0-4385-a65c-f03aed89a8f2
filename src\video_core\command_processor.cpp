#include "command_processor.h"
#include "../memory/memory.h"
#include "../memory/ps4_mmu.h"
#include "../ps4/ps4_gpu.h"
#include <chrono>
#include <mutex>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <vector>

namespace ps4 {

CommandProcessor::CommandProcessor(ps4::PS4MMU &mem, PS4GPU &gpu)
    : m_mmu(mem), m_gpu(gpu), m_stats{} {
  // CRITICAL FIX: Ensure all statistics are properly initialized to zero
  m_stats = {};
  spdlog::info("CommandProcessor constructed with zero-initialized stats");
}

CommandProcessor::~CommandProcessor() noexcept {
  Shutdown();
  spdlog::info("CommandProcessor destroyed");
}

bool CommandProcessor::Initialize() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    m_callStack.clear();
    m_packetCache.clear();
    m_commandQueue.clear();
    m_resourceDependencies.clear();
    m_pendingCommands.clear();
    m_cachedPipelineStates.clear();
    m_nextCommandId = 1;
    m_lastStateHash = 0;

    // Initialize pipeline state to default values
    m_pipelineState = GraphicsPipelineState{};
    m_pipelineState.lastUpdate = std::chrono::steady_clock::now();
    m_pipelineState.stateHash = ComputeStateHash();

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.cacheHits++;
    spdlog::info("CommandProcessor initialized with enhanced state tracking, "
                 "latency={}us",
                 latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("CommandProcessor initialization failed: {}", e.what());
    throw CommandProcessorException("Initialization failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    m_callStack.clear();
    m_packetCache.clear();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.cacheHits++;
    spdlog::info("CommandProcessor shutdown, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("CommandProcessor shutdown failed: {}", e.what());
  }
}

bool CommandProcessor::GetCachedPacketResult(
    uint64_t packetKey, std::vector<uint32_t> &data) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    auto it = m_packetCache.find(packetKey);
    if (it != m_packetCache.end() && it->second.processed) {
      data = it->second.data;
      it->second.cacheHits++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetCachedPacketResult: key=0x{:x}, hit, latency={}us",
                    packetKey, latency);
      return true;
    }
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetCachedPacketResult: key=0x{:x}, miss, latency={}us",
                  packetKey, latency);
    return false;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetCachedPacketResult failed: {}", e.what());
    return false;
  }
}

void CommandProcessor::ClearPacketCache() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    m_packetCache.clear();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ClearPacketCache: Cleared cache, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ClearPacketCache failed: {}", e.what());
  }
}

CommandProcessorStats CommandProcessor::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetStats: latency={}us", latency);
    return m_stats;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetStats failed: {}", e.what());
    return m_stats;
  }
}

uint32_t CommandProcessor::ReadDword(uint64_t address) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    uint32_t value = 0;
    if (!m_mmu.ReadVirtual(address, &value, sizeof(value), 0)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Memory read failed at 0x" +
                                      std::to_string(address));
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ReadDword: address=0x{:x}, value=0x{:x}, latency={}us",
                  address, value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ReadDword failed: {}", e.what());
    throw CommandProcessorException("ReadDword failed: " +
                                    std::string(e.what()));
  }
}

uint64_t CommandProcessor::ReadQword(uint64_t address) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    uint64_t value = 0;
    if (!m_mmu.ReadVirtual(address, &value, sizeof(value), 0)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Memory read failed at 0x" +
                                      std::to_string(address));
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ReadQword: address=0x{:x}, value=0x{:x}, latency={}us",
                  address, value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ReadQword failed: {}", e.what());
    throw CommandProcessorException("ReadQword failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessType0Packet(const PM4Header &header,
                                          const std::vector<uint32_t> &data,
                                          uint32_t &offset) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (offset + 1 + header.count() >= data.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid Type-0 packet size at offset=" +
                                      std::to_string(offset));
    }
    offset++;
    uint32_t baseAddress = data[offset++];
    std::vector<std::pair<uint32_t, uint32_t>> registerUpdates;
    for (uint32_t i = 0; i < header.count(); ++i) {
      uint32_t value = data[offset++];
      registerUpdates.emplace_back(baseAddress + i, value);
      spdlog::trace("ProcessType0Packet: Writing 0x{:08x} to context register "
                    "0x{:08x}",
                    value, baseAddress + i);
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessType0Packet: Processed {} registers starting at "
                  "0x{:08x}, latency={}us",
                  header.count(), baseAddress, latency);
    for (const auto &[regAddr, regValue] : registerUpdates) {
      m_gpu.SetContextRegister(regAddr, regValue);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessType0Packet failed: {}", e.what());
    throw CommandProcessorException("ProcessType0Packet failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessType1Packet(const PM4Header &header,
                                          const std::vector<uint32_t> &data,
                                          uint32_t &offset) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (offset + 1 + header.count() >= data.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid Type-1 packet size at offset=" +
                                      std::to_string(offset));
    }
    offset++;
    uint32_t baseAddress = data[offset++];
    PM4RegisterWrite regWrite;
    regWrite.baseAddress = baseAddress;
    std::vector<std::pair<uint32_t, uint32_t>> registerUpdates;
    for (uint16_t i = 0; i < header.count(); i++) {
      uint32_t value = data[offset++];
      regWrite.values.push_back(value);
      uint32_t regOffset = baseAddress + i;
      registerUpdates.emplace_back(regOffset, value);
      spdlog::trace(
          "ProcessType1Packet: Writing 0x{:08x} to context register 0x{:08x}",
          value, regOffset);
    }
    regWrite.cacheHits++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessType1Packet: Processed {} registers, latency={}us",
                  header.count(), latency);
    for (const auto &[regAddr, regValue] : registerUpdates) {
      m_gpu.SetContextRegister(regAddr, regValue);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessType1Packet failed: {}", e.what());
    throw CommandProcessorException("ProcessType1Packet failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessType2Packet(const PM4Header &header,
                                          uint32_t &offset,
                                          const std::vector<uint32_t> &data) {
  auto start = std::chrono::steady_clock::now();
  try {
    offset++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessType2Packet: NOP at offset={}, latency={}us", offset,
                  latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessType2Packet failed: {}", e.what());
  }
}

void CommandProcessor::ProcessNOP(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    address += count * sizeof(uint32_t);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessNOP: address=0x{:x}, count={}, latency={}us", address,
                  count, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessNOP failed: {}", e.what());
  }
}

void CommandProcessor::ProcessSetShaderReg(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid SET_SH_REG packet count: " +
                                      std::to_string(count));
    }

    // Create command execution context for dependency tracking
    CommandExecutionContext context;
    context.commandId = m_nextCommandId++;
    context.timestamp = std::chrono::steady_clock::now();
    context.estimatedCycles =
        (count - 1) * 2; // Estimate 2 cycles per register write
    context.requiresSync =
        false; // Register writes don't require immediate sync

    // Add pipeline state dependency
    CommandDependency stateDep;
    stateDep.type = CommandDependency::PIPELINE_STATE;
    stateDep.resourceId = 0; // General pipeline state
    stateDep.isWrite = true;
    context.dependencies.push_back(stateDep);

    std::vector<std::tuple<uint32_t, uint32_t, uint32_t>> shaderRegUpdates;
    uint32_t regOffset = ReadDword(address);
    address += sizeof(uint32_t);

    // Determine shader stage from register offset
    uint32_t shaderStage = 0; // Default to vertex shader
    if (regOffset >= 0x100 && regOffset < 0x200) {
      shaderStage = 1; // Hull shader
    } else if (regOffset >= 0x200 && regOffset < 0x300) {
      shaderStage = 2; // Domain shader
    } else if (regOffset >= 0x300 && regOffset < 0x400) {
      shaderStage = 3; // Geometry shader
    } else if (regOffset >= 0x400 && regOffset < 0x500) {
      shaderStage = 4; // Pixel shader
    } else if (regOffset >= 0x500 && regOffset < 0x600) {
      shaderStage = 5; // Compute shader
    }

    for (uint32_t i = 0; i < count - 1; i++) {
      uint32_t value = ReadDword(address);
      address += sizeof(uint32_t);
      uint32_t regAddr = regOffset + i;

      // Validate register write
      if (!ValidateShaderRegister(shaderStage, regAddr, value)) {
        spdlog::warn("ProcessSetShaderReg: Invalid shader register write to "
                     "stage {} reg 0x{:04x} with value 0x{:08x}",
                     shaderStage, regAddr, value);
        m_stats.errorCount++;
        // Continue processing but log the error
      }

      // Apply register constraints
      ApplyRegisterConstraints(value, regAddr, true);

      shaderRegUpdates.emplace_back(shaderStage, regAddr, value);

      // Check if this is a shader address register that affects pipeline state
      if (regAddr == 0x0 || regAddr == 0x1) {
        m_pipelineState.shaderStages[shaderStage].dirty = true;
        m_pipelineState.pipelineDirty = true;
      }

      spdlog::trace("ProcessSetShaderReg: Writing 0x{:08x} to shader register "
                    "0x{:04x} (stage {})",
                    value, regAddr, shaderStage);
    }

    // Track command dependencies
    TrackCommandDependencies(context);

    m_stats.cacheHits++;
    m_stats.stateChanges++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;

    spdlog::trace("ProcessSetShaderReg: Processed {} registers for stage {}, "
                  "latency={}us",
                  count - 1, shaderStage, latency);

    // Apply register updates to GPU
    for (const auto &[shaderType, regAddr, regValue] : shaderRegUpdates) {
      m_gpu.SetShaderRegister(shaderType, regAddr, regValue);
    }

  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessSetShaderReg failed: {}", e.what());
    throw CommandProcessorException("ProcessSetShaderReg failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessSetContextReg(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid SET_CONTEXT_REG packet count: " +
                                      std::to_string(count));
    }

    // Create command execution context for dependency tracking
    CommandExecutionContext context;
    context.commandId = m_nextCommandId++;
    context.timestamp = std::chrono::steady_clock::now();
    context.estimatedCycles =
        (count - 1) * 2; // Estimate 2 cycles per register write
    context.requiresSync =
        false; // Context register writes don't require immediate sync

    // Add pipeline state dependency
    CommandDependency stateDep;
    stateDep.type = CommandDependency::PIPELINE_STATE;
    stateDep.resourceId = 0; // General pipeline state
    stateDep.isWrite = true;
    context.dependencies.push_back(stateDep);

    std::vector<std::pair<uint32_t, uint32_t>> contextRegUpdates;
    uint32_t regOffset = ReadDword(address);
    address += sizeof(uint32_t);

    bool pipelineStateChanged = false;

    for (uint32_t i = 0; i < count - 1; i++) {
      uint32_t value = ReadDword(address);
      address += sizeof(uint32_t);
      uint32_t regAddr = regOffset + i;

      // Validate register write
      if (!ValidateContextRegister(regAddr, value)) {
        spdlog::warn("ProcessSetContextReg: Invalid register write to 0x{:04x} "
                     "with value 0x{:08x}",
                     regAddr, value);
        m_stats.errorCount++;
        // Continue processing but log the error
      }

      // Apply register constraints
      ApplyRegisterConstraints(value, regAddr, false);

      contextRegUpdates.emplace_back(regAddr, value);

      // Check if this register affects pipeline state
      if (regAddr >= 0x100 && regAddr < 0x108) {
        // Render target registers
        uint32_t rtIndex = regAddr - 0x100;
        if (rtIndex < 8) {
          m_pipelineState.colorTargets[rtIndex].dirty = true;
          if (value != 0) {
            m_pipelineState.colorTargets[rtIndex].enabled = true;
            m_pipelineState.colorTargets[rtIndex].surfaceId =
                static_cast<uint64_t>(value) << 8;
            m_pipelineState.activeColorTargets |= (1u << rtIndex);
          } else {
            m_pipelineState.colorTargets[rtIndex].enabled = false;
            m_pipelineState.activeColorTargets &= ~(1u << rtIndex);
          }
          pipelineStateChanged = true;
        }
      } else if (regAddr == 0x80) {
        // Depth/stencil control register
        m_pipelineState.depthStencil.dirty = true;
        pipelineStateChanged = true;
      } else if (regAddr >= 0x90 && regAddr < 0x98) {
        // Blend state registers
        m_pipelineState.blend.dirty = true;
        pipelineStateChanged = true;
      } else if (regAddr >= 0x200 && regAddr < 0x240) {
        // Vertex attribute registers
        uint32_t attrIndex = (regAddr - 0x200) / 4;
        if (attrIndex < 16) {
          // Mark pipeline as needing update for vertex attribute changes
          pipelineStateChanged = true;
        }
      }

      spdlog::trace(
          "ProcessSetContextReg: Writing 0x{:08x} to context register 0x{:04x}",
          value, regAddr);
    }

    // Mark pipeline as dirty if state-affecting registers were modified
    if (pipelineStateChanged) {
      m_pipelineState.pipelineDirty = true;
      m_pipelineState.renderPassDirty = true;
    }

    // Track command dependencies
    TrackCommandDependencies(context);

    m_stats.cacheHits++;
    m_stats.stateChanges++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;

    spdlog::trace("ProcessSetContextReg: Processed {} registers, "
                  "pipeline_changed={}, latency={}us",
                  count - 1, pipelineStateChanged, latency);

    // Apply register updates to GPU
    for (const auto &[regAddr, regValue] : contextRegUpdates) {
      m_gpu.SetContextRegister(regAddr, regValue);
    }

  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessSetContextReg failed: {}", e.what());
    throw CommandProcessorException("ProcessSetContextReg failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessDrawIndex(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 3) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid DRAW_INDEX packet count: " +
                                      std::to_string(count));
    }

    // Read draw parameters
    uint32_t indexCount = ReadDword(address);
    address += sizeof(uint32_t);
    uint64_t indexBufferBase = ReadQword(address);
    address += sizeof(uint64_t);

    // Setup vertex buffer bindings from current state
    SetupVertexBufferBindings();

    // Update pipeline state from current register values
    UpdatePipelineState();

    // Validate and setup index buffer
    if (indexBufferBase != 0) {
      ValidateIndexBuffer(indexBufferBase, indexCount);
    }

    // Validate draw state before execution
    ValidateDrawState();

    // Create command execution context for dependency tracking
    CommandExecutionContext context;
    context.commandId = m_nextCommandId++;
    context.timestamp = std::chrono::steady_clock::now();
    context.estimatedCycles = EstimatePacketCycles(PM4Header{}, {});
    context.requiresSync = true;

    // Add dependencies for index buffer
    if (indexBufferBase != 0) {
      CommandDependency indexDep;
      indexDep.type = CommandDependency::MEMORY_READ;
      indexDep.address = indexBufferBase;
      indexDep.size = indexCount * 4; // Assume 32-bit indices for now
      indexDep.isWrite = false;
      context.dependencies.push_back(indexDep);
    }

    // Add dependencies for active vertex buffers
    for (uint32_t i = 0; i < 16; ++i) {
      if (m_pipelineState.vertexBuffers[i].enabled) {
        CommandDependency vertexDep;
        vertexDep.type = CommandDependency::MEMORY_READ;
        vertexDep.address = m_pipelineState.vertexBuffers[i].address;
        vertexDep.size = m_pipelineState.vertexBuffers[i].size;
        vertexDep.isWrite = false;
        context.dependencies.push_back(vertexDep);
      }
    }

    // Add dependencies for active render targets
    for (uint32_t i = 0; i < 8; ++i) {
      if (m_pipelineState.colorTargets[i].enabled) {
        CommandDependency rtDep;
        rtDep.type = CommandDependency::RENDER_TARGET;
        rtDep.resourceId = m_pipelineState.colorTargets[i].surfaceId;
        rtDep.isWrite = true;
        context.dependencies.push_back(rtDep);
      }
    }

    // Track command dependencies
    TrackCommandDependencies(context);

    // Execute the draw command
    PM4DrawIndexPacket packet{indexCount, indexBufferBase, 0};
    packet.cacheHits++;
    m_stats.cacheHits++;
    m_stats.drawCalls++;

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;

    spdlog::trace("ProcessDrawIndex: Draw {} indices from buffer at 0x{:016x}, "
                  "dependencies={}, latency={}us",
                  indexCount, indexBufferBase, context.dependencies.size(),
                  latency);

    // Issue the actual draw call to GPU
    m_gpu.DrawIndex(indexCount, 1, 0, 0, 0);

  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessDrawIndex failed: {}", e.what());
    throw CommandProcessorException("ProcessDrawIndex failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessDispatchDirect(uint64_t &address,
                                             uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 3) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid DISPATCH_DIRECT packet count: " +
                                      std::to_string(count));
    }
    uint32_t threadGroupX = ReadDword(address);
    address += sizeof(uint32_t);
    uint32_t threadGroupY = ReadDword(address);
    address += sizeof(uint32_t);
    uint32_t threadGroupZ = ReadDword(address);
    address += sizeof(uint32_t);
    PM4DispatchPacket packet{threadGroupX, threadGroupY, threadGroupZ};
    packet.cacheHits++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessDispatchDirect: Dispatch compute shader with thread "
                  "groups: {}x{}x{}, latency={}us",
                  threadGroupX, threadGroupY, threadGroupZ, latency);
    m_gpu.Dispatch(threadGroupX, threadGroupY, threadGroupZ);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessDispatchDirect failed: {}", e.what());
    throw CommandProcessorException("ProcessDispatchDirect failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessWaitRegMem(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 5) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid WAIT_REG_MEM packet count: " +
                                      std::to_string(count));
    }
    uint32_t functionValue = ReadDword(address);
    address += sizeof(uint32_t);
    auto function =
        static_cast<PM4WaitRegMemPacket::Function>(functionValue & 0x7);
    auto memSpace =
        static_cast<PM4WaitRegMemPacket::MemSpace>((functionValue >> 4) & 0x1);
    uint64_t pollAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint32_t reference = ReadDword(address);
    address += sizeof(uint32_t);
    uint32_t mask = ReadDword(address);
    address += sizeof(uint32_t);
    uint32_t pollInterval = ReadDword(address);
    address += sizeof(uint32_t);
    PM4WaitRegMemPacket packet{function,  memSpace, pollAddress,
                               reference, mask,     pollInterval};
    packet.cacheHits++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessWaitRegMem: Wait for {}:{} to satisfy condition {}, "
                  "latency={}us",
                  memSpace == PM4WaitRegMemPacket::MemSpace::REGISTER
                      ? "register"
                      : "memory",
                  pollAddress, static_cast<uint32_t>(function), latency);
    m_gpu.WaitRegisterMemory(pollAddress, reference, mask,
                             static_cast<uint32_t>(function),
                             memSpace == PM4WaitRegMemPacket::MemSpace::MEMORY);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessWaitRegMem failed: {}", e.what());
    throw CommandProcessorException("ProcessWaitRegMem failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessAcquireMem(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 3) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid ACQUIRE_MEM packet count: " +
                                      std::to_string(count));
    }
    uint64_t memAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint32_t memSize = ReadDword(address);
    address += sizeof(uint32_t);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "ProcessAcquireMem: Acquire memory at 0x{:016x}, size={}, latency={}us",
        memAddress, memSize, latency);
    m_gpu.AcquireMemory(memAddress, memSize);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessAcquireMem failed: {}", e.what());
    throw CommandProcessorException("ProcessAcquireMem failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessReleaseMem(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 3) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid RELEASE_MEM packet count: " +
                                      std::to_string(count));
    }
    uint64_t memAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint32_t memSize = ReadDword(address);
    address += sizeof(uint32_t);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "ProcessReleaseMem: Release memory at 0x{:016x}, size={}, latency={}us",
        memAddress, memSize, latency);
    m_gpu.ReleaseMemory(memAddress, memSize);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessReleaseMem failed: {}", e.what());
    throw CommandProcessorException("ProcessReleaseMem failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessCall(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid CALL packet count: " +
                                      std::to_string(count));
    }
    uint64_t targetAddr = ReadQword(address);
    address += sizeof(uint64_t);
    m_callStack.push_back(address);
    address = targetAddr;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessCall: Call to 0x{:016x}, latency={}us", targetAddr,
                  latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessCall failed: {}", e.what());
    throw CommandProcessorException("ProcessCall failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessReturn(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (m_callStack.empty()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Empty call stack in RETURN packet");
    }
    address = m_callStack.back();
    m_callStack.pop_back();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessReturn: Return to address 0x{:x}, latency={}us",
                  address, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessReturn failed: {}", e.what());
    throw CommandProcessorException("ProcessReturn failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessIndexBufferSize(uint64_t &address,
                                              uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException(
          "Invalid INDEX_BUFFER_SIZE packet count: " + std::to_string(count));
    }
    uint32_t indexBufferSize = ReadDword(address);
    address += sizeof(uint32_t);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "ProcessIndexBufferSize: Set index buffer size: {}, latency={}us",
        indexBufferSize, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessIndexBufferSize failed: {}", e.what());
    throw CommandProcessorException("ProcessIndexBufferSize failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    uint64_t callStackSize = m_callStack.size();
    out.write(reinterpret_cast<const char *>(&callStackSize),
              sizeof(callStackSize));
    out.write(reinterpret_cast<const char *>(m_callStack.data()),
              callStackSize * sizeof(uint64_t));
    uint64_t cacheCount = m_packetCache.size();
    out.write(reinterpret_cast<const char *>(&cacheCount), sizeof(cacheCount));
    for (const auto &[key, entry] : m_packetCache) {
      out.write(reinterpret_cast<const char *>(&key), sizeof(key));
      out.write(reinterpret_cast<const char *>(&entry.header.value),
                sizeof(entry.header.value));
      uint32_t dataSize = static_cast<uint32_t>(entry.data.size());
      out.write(reinterpret_cast<const char *>(&dataSize), sizeof(dataSize));
      out.write(reinterpret_cast<const char *>(entry.data.data()),
                dataSize * sizeof(uint32_t));
      out.write(reinterpret_cast<const char *>(&entry.processed),
                sizeof(entry.processed));
      out.write(reinterpret_cast<const char *>(&entry.cacheHits),
                sizeof(entry.cacheHits));
      out.write(reinterpret_cast<const char *>(&entry.cacheMisses),
                sizeof(entry.cacheMisses));
    }
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));
    if (!out.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException(
          "Failed to write command processor state");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SaveState: Saved command processor state, "
                 "call_stack_size={}, cache_count={}, latency={}us",
                 callStackSize, cacheCount, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SaveState failed: {}", e.what());
    throw CommandProcessorException("SaveState failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException(
          "Unsupported command processor state version: " +
          std::to_string(version));
    }
    uint64_t callStackSize;
    in.read(reinterpret_cast<char *>(&callStackSize), sizeof(callStackSize));
    m_callStack.resize(callStackSize);
    in.read(reinterpret_cast<char *>(m_callStack.data()),
            callStackSize * sizeof(uint64_t));
    m_packetCache.clear();
    uint64_t cacheCount;
    in.read(reinterpret_cast<char *>(&cacheCount), sizeof(cacheCount));
    for (uint64_t i = 0; i < cacheCount && in.good(); ++i) {
      uint64_t key;
      PacketCacheEntry entry;
      in.read(reinterpret_cast<char *>(&key), sizeof(key));
      in.read(reinterpret_cast<char *>(&entry.header.value),
              sizeof(entry.header.value));
      uint32_t dataSize;
      in.read(reinterpret_cast<char *>(&dataSize), sizeof(dataSize));
      entry.data.resize(dataSize);
      in.read(reinterpret_cast<char *>(entry.data.data()),
              dataSize * sizeof(uint32_t));
      in.read(reinterpret_cast<char *>(&entry.processed),
              sizeof(entry.processed));
      in.read(reinterpret_cast<char *>(&entry.cacheHits),
              sizeof(entry.cacheHits));
      in.read(reinterpret_cast<char *>(&entry.cacheMisses),
              sizeof(entry.cacheMisses));
      m_packetCache[key] = entry;
    }
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
    if (!in.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Failed to read command processor state");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("LoadState: Loaded command processor state, "
                 "call_stack_size={}, cache_count={}, latency={}us",
                 callStackSize, cacheCount, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("LoadState failed: {}", e.what());
    throw CommandProcessorException("LoadState failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::NotifySurfaceCreated(uint32_t surfaceId, uint32_t width,
                                            uint32_t height, uint32_t format) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    spdlog::trace("NotifySurfaceCreated: id={}, size={}x{}, format=0x{:x}",
                  surfaceId, width, height, format);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("NotifySurfaceCreated failed: {}", e.what());
  }
}

void CommandProcessor::NotifySurfaceDestroyed(uint32_t surfaceId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    spdlog::trace("NotifySurfaceDestroyed: id={}", surfaceId);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("NotifySurfaceDestroyed failed: {}", e.what());
  }
}

void CommandProcessor::NotifySurfaceUpdated(uint32_t surfaceId, uint32_t x,
                                            uint32_t y, uint32_t width,
                                            uint32_t height) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    spdlog::trace("NotifySurfaceUpdated: id={}, region={}x{} at ({},{})",
                  surfaceId, width, height, x, y);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("NotifySurfaceUpdated failed: {}", e.what());
  }
}

void CommandProcessor::NotifySurfaceClear(uint32_t surfaceId,
                                          uint32_t clearValue) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    spdlog::trace("NotifySurfaceClear: id={}, clearValue=0x{:x}", surfaceId,
                  clearValue);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("NotifySurfaceClear failed: {}", e.what());
  }
}

void CommandProcessor::NotifySurfaceCopy(uint32_t srcSurfaceId,
                                         uint32_t dstSurfaceId, uint32_t srcX,
                                         uint32_t srcY, uint32_t dstX,
                                         uint32_t dstY, uint32_t width,
                                         uint32_t height) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    spdlog::trace("NotifySurfaceCopy: src={} dst={}, srcPos=({},{}) "
                  "dstPos=({},{}) size={}x{}",
                  srcSurfaceId, dstSurfaceId, srcX, srcY, dstX, dstY, width,
                  height);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("NotifySurfaceCopy failed: {}", e.what());
  }
}

bool CommandProcessor::ProcessEnhancedPM4Packet(
    EnhancedPM4Opcode opcode, const std::vector<uint32_t> &data) {
  auto start = std::chrono::steady_clock::now();
  try {
    uint64_t address = 0;
    uint32_t count = static_cast<uint32_t>(data.size());
    switch (opcode) {
    case EnhancedPM4Opcode::DRAW_INDEX:
      ProcessDrawIndex(address, count);
      break;
    case EnhancedPM4Opcode::DRAW_INDEX_AUTO:
      ProcessDrawIndexAuto(address, count);
      break;
    case EnhancedPM4Opcode::DRAW_INDIRECT:
      ProcessDrawIndirect(address, count);
      break;
    case EnhancedPM4Opcode::DRAW_INDEX_INDIRECT:
      ProcessDrawIndexIndirect(address, count);
      break;
    case EnhancedPM4Opcode::DRAW_INDEX_MULTI:
      ProcessDrawIndexMulti(address, count);
      break;
    case EnhancedPM4Opcode::SET_CONTEXT_REG:
      ProcessSetContextReg(address, count);
      break;
    case EnhancedPM4Opcode::SET_CONFIG_REG:
      ProcessSetConfigReg(address, count);
      break;
    case EnhancedPM4Opcode::SET_SH_REG:
      ProcessSetShaderReg(address, count);
      break;
    case EnhancedPM4Opcode::SET_UCONFIG_REG:
      ProcessSetUConfigReg(address, count);
      break;
    case EnhancedPM4Opcode::LOAD_CONTEXT_REG:
      ProcessLoadContextReg(address, count);
      break;
    case EnhancedPM4Opcode::LOAD_SH_REG:
      ProcessLoadShaderReg(address, count);
      break;
    case EnhancedPM4Opcode::DMA_DATA:
      ProcessDMAData(address, count);
      break;
    case EnhancedPM4Opcode::COPY_DATA:
      ProcessCopyData(address, count);
      break;
    case EnhancedPM4Opcode::WRITE_DATA:
      ProcessWriteData(address, count);
      break;
    case EnhancedPM4Opcode::READ_DATA:
      ProcessReadData(address, count);
      break;
    case EnhancedPM4Opcode::EVENT_WRITE:
      ProcessEventWrite(address, count);
      break;
    case EnhancedPM4Opcode::EVENT_WRITE_EOP:
      ProcessEventWriteEOP(address, count);
      break;
    case EnhancedPM4Opcode::WAIT_REG_MEM:
      ProcessWaitRegMem(address, count);
      break;
    case EnhancedPM4Opcode::SURFACE_SYNC:
      ProcessSurfaceSync(address, count);
      break;
    case EnhancedPM4Opcode::DISPATCH_DIRECT:
      ProcessDispatchDirect(address, count);
      break;
    case EnhancedPM4Opcode::DISPATCH_INDIRECT:
      ProcessDispatchIndirect(address, count);
      break;
    case EnhancedPM4Opcode::ACQUIRE_MEM:
      ProcessAcquireMem(address, count);
      break;
    case EnhancedPM4Opcode::RELEASE_MEM:
      ProcessReleaseMem(address, count);
      break;
    case EnhancedPM4Opcode::SET_SAMPLER:
      ProcessSetSampler(address, count);
      break;
    case EnhancedPM4Opcode::SET_RESOURCE:
      ProcessSetResource(address, count);
      break;
    case EnhancedPM4Opcode::SET_SHADER:
      ProcessSetShader(address, count);
      break;
    case EnhancedPM4Opcode::LOAD_SHADER:
      ProcessLoadShader(address, count);
      break;
    case EnhancedPM4Opcode::CALL:
      ProcessCall(address, count);
      break;
    case EnhancedPM4Opcode::RETURN:
      ProcessReturn(address, count);
      break;
    case EnhancedPM4Opcode::INDIRECT_BUFFER:
      ProcessIndirectBuffer(address, count);
      break;
    case EnhancedPM4Opcode::INDEX_BUFFER_SIZE:
      ProcessIndexBufferSize(address, count);
      break;
    case EnhancedPM4Opcode::UNKNOWN:
    default:
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::warn("ProcessEnhancedPM4Packet: Unknown opcode: 0x{:02x}",
                   static_cast<uint32_t>(opcode));
      return false;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessEnhancedPM4Packet: opcode=0x{:02x}, latency={}us",
                  static_cast<uint32_t>(opcode), latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessEnhancedPM4Packet failed: {}", e.what());
    return false;
  }
}

bool CommandProcessor::ValidatePacketData(const PM4Header &header,
                                          const std::vector<uint32_t> &data) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (data.size() < header.count() + 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ValidatePacketData: Insufficient data for packet type={}, "
                    "opcode=0x{:02x}, expected={} dwords, got={}",
                    static_cast<uint32_t>(header.type()),
                    static_cast<uint32_t>(header.opcode()), header.count() + 1,
                    data.size());
      return false;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ValidatePacketData: Validated packet type={}, "
                  "opcode=0x{:02x}, latency={}us",
                  static_cast<uint32_t>(header.type()),
                  static_cast<uint32_t>(header.opcode()), latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ValidatePacketData failed: {}", e.what());
    return false;
  }
}

void CommandProcessor::OptimizeCommandSequence(
    std::vector<PM4Header> &headers, std::vector<std::vector<uint32_t>> &data) {
  auto start = std::chrono::steady_clock::now();
  try {
    std::vector<PM4Header> optimizedHeaders;
    std::vector<std::vector<uint32_t>> optimizedData;
    for (size_t i = 0; i < headers.size(); ++i) {
      if (headers[i].type() == PM4PacketType::TYPE_3 &&
          headers[i].opcode() == PM4Opcode::NOP) {
        spdlog::trace("OptimizeCommandSequence: Removed NOP packet at index {}",
                      i);
        continue;
      }
      optimizedHeaders.push_back(headers[i]);
      optimizedData.push_back(data[i]);
    }
    headers = std::move(optimizedHeaders);
    data = std::move(optimizedData);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("OptimizeCommandSequence: Optimized {} packets, latency={}us",
                  headers.size(), latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("OptimizeCommandSequence failed: {}", e.what());
  }
}

uint32_t
CommandProcessor::EstimatePacketCycles(const PM4Header &header,
                                       const std::vector<uint32_t> &data) {
  try {
    uint32_t cycles = 0;
    switch (header.type()) {
    case PM4PacketType::TYPE_0:
      cycles = header.count() * 2;
      break;
    case PM4PacketType::TYPE_1:
      cycles = header.count() * 2;
      break;
    case PM4PacketType::TYPE_2:
      cycles = 1;
      break;
    case PM4PacketType::TYPE_3:
      switch (header.opcode()) {
      case PM4Opcode::NOP:
        cycles = 1;
        break;
      case PM4Opcode::SET_SH_REG:
      case PM4Opcode::SET_CONTEXT_REG:
        cycles = header.count() * 2;
        break;
      case PM4Opcode::DRAW_INDEX:
        cycles = 50;
        break;
      case PM4Opcode::DISPATCH_DIRECT:
        cycles = 40;
        break;
      case PM4Opcode::WAIT_REG_MEM:
        cycles = 20;
        break;
      case PM4Opcode::ACQUIRE_MEM:
      case PM4Opcode::RELEASE_MEM:
        cycles = 30;
        break;
      case PM4Opcode::CALL:
      case PM4Opcode::RETURN:
        cycles = 10;
        break;
      case PM4Opcode::INDIRECT_BUFFER:
        cycles = 100;
        break;
      case PM4Opcode::INDEX_BUFFER_SIZE:
        cycles = 5;
        break;
      default:
        cycles = 10;
        break;
      }
      break;
    default:
      cycles = 10;
      break;
    }
    return cycles;
  } catch (const std::exception &e) {
    spdlog::error("EstimatePacketCycles failed: {}", e.what());
    return 10;
  }
}

void CommandProcessor::ProcessDrawIndexAuto(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid DRAW_INDEX_AUTO packet count: " +
                                      std::to_string(count));
    }
    uint32_t vertexCount = ReadDword(address);
    address += sizeof(uint32_t);
    m_stats.drawCalls++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessDrawIndexAuto: Draw {} vertices, latency={}us",
                  vertexCount, latency);
    m_gpu.DrawIndex(vertexCount, 1, 0, 0, 0);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessDrawIndexAuto failed: {}", e.what());
    throw CommandProcessorException("ProcessDrawIndexAuto failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessDrawIndirect(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid DRAW_INDIRECT packet count: " +
                                      std::to_string(count));
    }
    uint64_t dataAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint32_t vertexCount = ReadDword(dataAddress);
    m_stats.drawCalls++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "ProcessDrawIndirect: Draw {} vertices from 0x{:016x}, latency={}us",
        vertexCount, dataAddress, latency);
    m_gpu.DrawIndex(vertexCount, 1, 0, 0, 0);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessDrawIndirect failed: {}", e.what());
    throw CommandProcessorException("ProcessDrawIndirect failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessDrawIndexIndirect(uint64_t &address,
                                                uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException(
          "Invalid DRAW_INDEX_INDIRECT packet count: " + std::to_string(count));
    }
    uint64_t dataAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint32_t indexCount = ReadDword(dataAddress);
    m_stats.drawCalls++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessDrawIndexIndirect: Draw {} indices from 0x{:016x}, "
                  "latency={}us",
                  indexCount, dataAddress, latency);
    m_gpu.DrawIndex(indexCount, 1, 0, 0, 0);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessDrawIndexIndirect failed: {}", e.what());
    throw CommandProcessorException("ProcessDrawIndexIndirect failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessDrawIndexMulti(uint64_t &address,
                                             uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 3) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException(
          "Invalid DRAW_INDEX_MULTI packet count: " + std::to_string(count));
    }

    uint32_t drawCount = ReadDword(address);
    address += sizeof(uint32_t);
    uint64_t dataAddress = ReadQword(address);
    address += sizeof(uint64_t);

    // Read multi-draw data from memory
    std::vector<uint32_t> drawData;
    drawData.reserve(drawCount * 3);

    for (uint32_t i = 0; i < drawCount; i++) {
      // Each draw command has: indexCount, instanceCount, firstIndex
      uint32_t indexCount = ReadDword(dataAddress + i * 12);
      uint32_t instanceCount = ReadDword(dataAddress + i * 12 + 4);
      uint32_t firstIndex = ReadDword(dataAddress + i * 12 + 8);

      drawData.push_back(indexCount);
      drawData.push_back(instanceCount);
      drawData.push_back(firstIndex);
    }

    // Use the advanced multi-draw processing
    ProcessMultiDrawCommands(drawData);

    m_stats.drawCalls += drawCount;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;

    spdlog::trace("ProcessDrawIndexMulti: Processed {} draws from 0x{:016x}, "
                  "latency={}us",
                  drawCount, dataAddress, latency);

  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessDrawIndexMulti failed: {}", e.what());
    throw CommandProcessorException("ProcessDrawIndexMulti failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessSetConfigReg(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid SET_CONFIG_REG packet count: " +
                                      std::to_string(count));
    }
    std::vector<std::pair<uint32_t, uint32_t>> configRegUpdates;
    uint32_t regOffset = ReadDword(address);
    address += sizeof(uint32_t);
    for (uint32_t i = 0; i < count - 1; i++) {
      uint32_t value = ReadDword(address);
      address += sizeof(uint32_t);
      configRegUpdates.emplace_back(regOffset + i, value);
      spdlog::trace(
          "ProcessSetConfigReg: Writing 0x{:08x} to config register 0x{:04x}",
          value, regOffset + i);
    }
    m_stats.stateChanges += count - 1;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessSetConfigReg: Processed {} registers, latency={}us",
                  count - 1, latency);
    for (const auto &[regAddr, regValue] : configRegUpdates) {
      m_gpu.SetContextRegister(regAddr, regValue);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessSetConfigReg failed: {}", e.what());
    throw CommandProcessorException("ProcessSetConfigReg failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessSetUConfigReg(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid SET_UCONFIG_REG packet count: " +
                                      std::to_string(count));
    }
    std::vector<std::pair<uint32_t, uint32_t>> uconfigRegUpdates;
    uint32_t regOffset = ReadDword(address);
    address += sizeof(uint32_t);
    for (uint32_t i = 0; i < count - 1; i++) {
      uint32_t value = ReadDword(address);
      address += sizeof(uint32_t);
      uconfigRegUpdates.emplace_back(regOffset + i, value);
      spdlog::trace(
          "ProcessSetUConfigReg: Writing 0x{:08x} to uconfig register 0x{:04x}",
          value, regOffset + i);
    }
    m_stats.stateChanges += count - 1;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessSetUConfigReg: Processed {} registers, latency={}us",
                  count - 1, latency);
    for (const auto &[regAddr, regValue] : uconfigRegUpdates) {
      m_gpu.SetContextRegister(regAddr, regValue);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessSetUConfigReg failed: {}", e.what());
    throw CommandProcessorException("ProcessSetUConfigReg failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessLoadContextReg(uint64_t &address,
                                             uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException(
          "Invalid LOAD_CONTEXT_REG packet count: " + std::to_string(count));
    }
    uint64_t dataAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint32_t regCount = ReadDword(address);
    address += sizeof(uint32_t);
    m_stats.stateChanges += regCount;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "ProcessLoadContextReg: Load {} registers from 0x{:016x}, latency={}us",
        regCount, dataAddress, latency);
    for (uint32_t i = 0; i < regCount; i++) {
      uint32_t regOffset = ReadDword(dataAddress + i * 8);
      uint32_t regValue = ReadDword(dataAddress + i * 8 + 4);
      m_gpu.SetContextRegister(regOffset, regValue);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessLoadContextReg failed: {}", e.what());
    throw CommandProcessorException("ProcessLoadContextReg failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessLoadShaderReg(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid LOAD_SH_REG packet count: " +
                                      std::to_string(count));
    }
    uint64_t dataAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint32_t regCount = ReadDword(address);
    address += sizeof(uint32_t);
    m_stats.stateChanges += regCount;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessLoadShaderReg: Load {} shader registers from "
                  "0x{:016x}, latency={}us",
                  regCount, dataAddress, latency);
    for (uint32_t i = 0; i < regCount; i++) {
      uint32_t regOffset = ReadDword(dataAddress + i * 8);
      uint32_t regValue = ReadDword(dataAddress + i * 8 + 4);
      m_gpu.SetShaderRegister(0, regOffset, regValue);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessLoadShaderReg failed: {}", e.what());
    throw CommandProcessorException("ProcessLoadShaderReg failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessDMAData(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 4) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid DMA_DATA packet count: " +
                                      std::to_string(count));
    }
    uint64_t srcAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint64_t dstAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint32_t size = ReadDword(address);
    address += sizeof(uint32_t);
    m_stats.memoryOperations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessDMAData: DMA from 0x{:016x} to 0x{:016x}, size={}, "
                  "latency={}us",
                  srcAddress, dstAddress, size, latency);
    std::vector<uint8_t> tempBuffer(size);
    if (m_mmu.ReadVirtual(srcAddress, tempBuffer.data(), size, 0)) {
      m_mmu.WriteVirtual(dstAddress, tempBuffer.data(), size, 0);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessDMAData failed: {}", e.what());
    throw CommandProcessorException("ProcessDMAData failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessCopyData(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 4) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid COPY_DATA packet count: " +
                                      std::to_string(count));
    }
    uint64_t srcAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint64_t dstAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint32_t size = ReadDword(address);
    address += sizeof(uint32_t);
    m_stats.memoryOperations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessCopyData: Copy from 0x{:016x} to 0x{:016x}, size={}, "
                  "latency={}us",
                  srcAddress, dstAddress, size, latency);
    std::vector<uint8_t> tempBuffer(size);
    if (m_mmu.ReadVirtual(srcAddress, tempBuffer.data(), size, 0)) {
      m_mmu.WriteVirtual(dstAddress, tempBuffer.data(), size, 0);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessCopyData failed: {}", e.what());
    throw CommandProcessorException("ProcessCopyData failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessWriteData(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid WRITE_DATA packet count: " +
                                      std::to_string(count));
    }
    uint64_t dstAddress = ReadQword(address);
    address += sizeof(uint64_t);
    std::vector<uint32_t> data;
    for (uint32_t i = 0; i < count - 2; i++) {
      data.push_back(ReadDword(address));
      address += sizeof(uint32_t);
    }
    m_stats.memoryOperations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "ProcessWriteData: Write {} dwords to 0x{:016x}, latency={}us",
        data.size(), dstAddress, latency);
    m_mmu.WriteVirtual(dstAddress, data.data(), data.size() * sizeof(uint32_t),
                       0);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessWriteData failed: {}", e.what());
    throw CommandProcessorException("ProcessWriteData failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessReadData(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid READ_DATA packet count: " +
                                      std::to_string(count));
    }
    uint64_t srcAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint32_t size = ReadDword(address);
    address += sizeof(uint32_t);
    m_stats.memoryOperations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessReadData: Read {} bytes from 0x{:016x}, latency={}us",
                  size, srcAddress, latency);
    std::vector<uint8_t> data(size);
    m_mmu.ReadVirtual(srcAddress, data.data(), size, 0);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessReadData failed: {}", e.what());
    throw CommandProcessorException("ProcessReadData failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessEventWrite(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid EVENT_WRITE packet count: " +
                                      std::to_string(count));
    }
    uint32_t eventId = ReadDword(address);
    address += sizeof(uint32_t);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessEventWrite: Event ID=0x{:x}, latency={}us", eventId,
                  latency);
    spdlog::debug("ProcessEventWrite: Event ID=0x{:x} processed", eventId);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessEventWrite failed: {}", e.what());
    throw CommandProcessorException("ProcessEventWrite failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessEventWriteEOP(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 3) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid EVENT_WRITE_EOP packet count: " +
                                      std::to_string(count));
    }
    uint32_t eventId = ReadDword(address);
    address += sizeof(uint32_t);
    uint64_t dstAddress = ReadQword(address);
    address += sizeof(uint64_t);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessEventWriteEOP: Event ID=0x{:x}, address=0x{:016x}, "
                  "latency={}us",
                  eventId, dstAddress, latency);
    spdlog::debug(
        "ProcessEventWriteEOP: Event ID=0x{:x} at address=0x{:016x} processed",
        eventId, dstAddress);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessEventWriteEOP failed: {}", e.what());
    throw CommandProcessorException("ProcessEventWriteEOP failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessSurfaceSync(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid SURFACE_SYNC packet count: " +
                                      std::to_string(count));
    }
    uint32_t surfaceId = ReadDword(address);
    address += sizeof(uint32_t);
    uint32_t flags = ReadDword(address);
    address += sizeof(uint32_t);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "ProcessSurfaceSync: Surface ID={}, flags=0x{:x}, latency={}us",
        surfaceId, flags, latency);
    spdlog::debug("ProcessSurfaceSync: Surface ID={}, flags=0x{:x} processed",
                  surfaceId, flags);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessSurfaceSync failed: {}", e.what());
    throw CommandProcessorException("ProcessSurfaceSync failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessDispatchIndirect(uint64_t &address,
                                               uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException(
          "Invalid DISPATCH_INDIRECT packet count: " + std::to_string(count));
    }
    uint64_t dataAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint32_t threadGroupX = ReadDword(dataAddress);
    uint32_t threadGroupY = ReadDword(dataAddress + 4);
    uint32_t threadGroupZ = ReadDword(dataAddress + 8);
    m_stats.computeDispatches++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessDispatchIndirect: Dispatch {}x{}x{} from 0x{:016x}, "
                  "latency={}us",
                  threadGroupX, threadGroupY, threadGroupZ, dataAddress,
                  latency);
    m_gpu.Dispatch(threadGroupX, threadGroupY, threadGroupZ);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessDispatchIndirect failed: {}", e.what());
    throw CommandProcessorException("ProcessDispatchIndirect failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessSetSampler(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid SET_SAMPLER packet count: " +
                                      std::to_string(count));
    }
    uint32_t samplerId = ReadDword(address);
    address += sizeof(uint32_t);
    std::vector<uint32_t> samplerData;
    for (uint32_t i = 0; i < count - 1; i++) {
      samplerData.push_back(ReadDword(address));
      address += sizeof(uint32_t);
    }
    m_stats.stateChanges++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "ProcessSetSampler: Set sampler ID={}, data count={}, latency={}us",
        samplerId, samplerData.size(), latency);
    spdlog::debug(
        "ProcessSetSampler: Sampler ID={} with {} data elements processed",
        samplerId, samplerData.size());
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessSetSampler failed: {}", e.what());
    throw CommandProcessorException("ProcessSetSampler failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessSetResource(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid SET_RESOURCE packet count: " +
                                      std::to_string(count));
    }
    uint32_t resourceId = ReadDword(address);
    address += sizeof(uint32_t);
    std::vector<uint32_t> resourceData;
    for (uint32_t i = 0; i < count - 1; i++) {
      resourceData.push_back(ReadDword(address));
      address += sizeof(uint32_t);
    }
    m_stats.stateChanges++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "ProcessSetResource: Set resource ID={}, data count={}, latency={}us",
        resourceId, resourceData.size(), latency);
    spdlog::debug(
        "ProcessSetResource: Resource ID={} with {} data elements processed",
        resourceId, resourceData.size());
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessSetResource failed: {}", e.what());
    throw CommandProcessorException("ProcessSetResource failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessSetShader(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid SET_SHADER packet count: " +
                                      std::to_string(count));
    }
    uint32_t shaderType = ReadDword(address);
    address += sizeof(uint32_t);
    uint64_t shaderAddress = ReadQword(address);
    address += sizeof(uint64_t);
    m_stats.stateChanges++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "ProcessSetShader: Set shader type={} at 0x{:016x}, latency={}us",
        shaderType, shaderAddress, latency);
    spdlog::debug(
        "ProcessSetShader: Shader type={} at address=0x{:016x} processed",
        shaderType, shaderAddress);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessSetShader failed: {}", e.what());
    throw CommandProcessorException("ProcessSetShader failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessLoadShader(uint64_t &address, uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 2) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid LOAD_SHADER packet count: " +
                                      std::to_string(count));
    }
    uint32_t shaderType = ReadDword(address);
    address += sizeof(uint32_t);
    uint64_t shaderAddress = ReadQword(address);
    address += sizeof(uint64_t);
    m_stats.stateChanges++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "ProcessLoadShader: Load shader type={} from 0x{:016x}, latency={}us",
        shaderType, shaderAddress, latency);
    spdlog::debug(
        "ProcessLoadShader: Shader type={} from address=0x{:016x} processed",
        shaderType, shaderAddress);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessLoadShader failed: {}", e.what());
    throw CommandProcessorException("ProcessLoadShader failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessIndirectBuffer(uint64_t &address,
                                             uint32_t count) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (count < 3) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid INDIRECT_BUFFER packet count: " +
                                      std::to_string(count));
    }
    uint64_t bufferAddress = ReadQword(address);
    address += sizeof(uint64_t);
    uint32_t bufferSize = ReadDword(address);
    address += sizeof(uint32_t);
    PM4IndirectBufferPacket packet{bufferAddress, bufferSize};
    packet.cacheHits++;
    m_indirectBufferQueue.push_back({bufferAddress, bufferSize});
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessIndirectBuffer: Queued indirect buffer at 0x{:016x}, "
                  "size={} dwords, latency={}us",
                  bufferAddress, bufferSize, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessIndirectBuffer failed: {}", e.what());
    throw CommandProcessorException("ProcessIndirectBuffer failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessCommandBuffer(uint64_t addr, size_t size) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    std::vector<uint32_t> packetData(size / 4, 0);
    if (!m_mmu.ReadVirtual(addr, packetData.data(), size, 0)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Failed to read command buffer at 0x" +
                                      std::to_string(addr));
    }
    uint32_t offset = 0;
    while (offset < packetData.size()) {
      PM4Header header;
      if (offset >= packetData.size()) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        spdlog::error("Incomplete header at offset {}", offset);
        break;
      }
      header.value = packetData[offset];

      // Check for debug breakpoint
      if (CheckDebugBreakpoint(static_cast<uint32_t>(header.opcode()))) {
        spdlog::warn("ProcessCommandBuffer: Debug breakpoint hit at offset {}, "
                     "opcode=0x{:02x}",
                     offset, static_cast<uint32_t>(header.opcode()));
        // In a real debugger, we would pause execution here
        if (m_debugMode) {
          std::ostringstream ss;
          DumpPipelineState(ss);
          spdlog::debug("Pipeline state at breakpoint:\n{}", ss.str());
        }
      }

      uint64_t cacheKey = header.cacheKey();
      std::vector<uint32_t> cachedData;
      if (GetCachedPacketResult(cacheKey, cachedData)) {
        m_stats.cacheHits++;
        offset += header.count() + 1;
        m_stats.packetCount++;
        switch (header.type()) {
        case PM4PacketType::TYPE_0:
          m_stats.type0Packets++;
          break;
        case PM4PacketType::TYPE_1:
          m_stats.type1Packets++;
          break;
        case PM4PacketType::TYPE_2:
          m_stats.type2Packets++;
          break;
        case PM4PacketType::TYPE_3:
          m_stats.type3Packets++;
          break;
        }
        spdlog::trace("ProcessCommandBuffer: Cached packet at offset {}, "
                      "type={}, opcode=0x{:x}",
                      offset, static_cast<uint32_t>(header.type()),
                      static_cast<uint32_t>(header.opcode()));
        continue;
      }
      PacketCacheEntry cacheEntry{
          header,
          std::vector<uint32_t>(packetData.begin() + offset,
                                packetData.begin() + offset + header.count() +
                                    1),
          false};
      if (header.type() == PM4PacketType::TYPE_0) {
        m_stats.type0Packets++;
        ProcessType0Packet(header, packetData, offset);
      } else if (header.type() == PM4PacketType::TYPE_1) {
        m_stats.type1Packets++;
        ProcessType1Packet(header, packetData, offset);
      } else if (header.type() == PM4PacketType::TYPE_2) {
        m_stats.type2Packets++;
        ProcessType2Packet(header, offset, packetData);
      } else if (header.type() == PM4PacketType::TYPE_3) {
        m_stats.type3Packets++;
        ProcessType3Packet(header, offset, packetData);
      } else {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        spdlog::error("Unknown PM4 packet type: {}",
                      static_cast<uint32_t>(header.type()));
        offset += header.count() + 1;
      }
      cacheEntry.processed = true;
      m_packetCache[cacheKey] = cacheEntry;
      m_stats.packetCount++;
      m_gpu.NotifyPacketProcessed(header.value, cacheEntry.data);
    }
    while (!m_indirectBufferQueue.empty()) {
      IndirectBufferQueueEntry entry = m_indirectBufferQueue.front();
      m_indirectBufferQueue.erase(m_indirectBufferQueue.begin());
      spdlog::trace("ProcessCommandBuffer: Processing queued indirect buffer "
                    "at 0x{:016x}, size={} dwords",
                    entry.bufferAddress, entry.bufferSize);
      ProcessCommandBuffer(entry.bufferAddress,
                           entry.bufferSize * sizeof(uint32_t));
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("ProcessCommandBuffer: Processed buffer at 0x{:x}, size={}, "
                 "packets={}, latency={}us",
                 addr, size, m_stats.packetCount, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessCommandBuffer failed: {}", e.what());
    throw CommandProcessorException("ProcessCommandBuffer failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::ProcessType3Packet(const PM4Header &header,
                                          uint32_t &offset,
                                          const std::vector<uint32_t> &data) {
  auto start = std::chrono::steady_clock::now();
  try {
    spdlog::trace("ProcessType3Packet: offset={}, opcode=0x{:02x}, count={}",
                  offset, static_cast<uint32_t>(header.opcode()),
                  header.count());
    offset++;
    uint64_t address = offset * sizeof(uint32_t);
    std::vector<uint32_t> packetData(data.begin() + offset,
                                     data.begin() + offset + header.count());
    if (!ValidatePacketData(header, packetData)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw CommandProcessorException("Invalid Type-3 packet data");
    }
    EnhancedPM4Opcode enhancedOpcode;
    switch (header.opcode()) {
    case PM4Opcode::NOP:
      enhancedOpcode = EnhancedPM4Opcode::NOP;
      break;
    case PM4Opcode::SET_SH_REG:
      enhancedOpcode = EnhancedPM4Opcode::SET_SH_REG;
      break;
    case PM4Opcode::SET_CONTEXT_REG:
      enhancedOpcode = EnhancedPM4Opcode::SET_CONTEXT_REG;
      break;
    case PM4Opcode::DRAW_INDEX:
      enhancedOpcode = EnhancedPM4Opcode::DRAW_INDEX;
      break;
    case PM4Opcode::DISPATCH_DIRECT:
      enhancedOpcode = EnhancedPM4Opcode::DISPATCH_DIRECT;
      break;
    case PM4Opcode::WAIT_REG_MEM:
      enhancedOpcode = EnhancedPM4Opcode::WAIT_REG_MEM;
      break;
    case PM4Opcode::ACQUIRE_MEM:
      enhancedOpcode = EnhancedPM4Opcode::ACQUIRE_MEM;
      break;
    case PM4Opcode::RELEASE_MEM:
      enhancedOpcode = EnhancedPM4Opcode::RELEASE_MEM;
      break;
    case PM4Opcode::CALL:
      enhancedOpcode = EnhancedPM4Opcode::CALL;
      break;
    case PM4Opcode::RETURN:
      enhancedOpcode = EnhancedPM4Opcode::RETURN;
      break;
    case PM4Opcode::INDIRECT_BUFFER:
      enhancedOpcode = EnhancedPM4Opcode::INDIRECT_BUFFER;
      break;
    case PM4Opcode::INDEX_BUFFER_SIZE:
      enhancedOpcode = EnhancedPM4Opcode::INDEX_BUFFER_SIZE;
      break;
    default:
      switch (static_cast<uint8_t>(header.opcode())) {
      case 0x11:
        enhancedOpcode = EnhancedPM4Opcode::DRAW_INDEX_AUTO;
        break;
      case 0x12:
        enhancedOpcode = EnhancedPM4Opcode::DRAW_INDIRECT;
        break;
      case 0x13:
        enhancedOpcode = EnhancedPM4Opcode::DRAW_INDEX_INDIRECT;
        break;
      case 0x14:
        enhancedOpcode = EnhancedPM4Opcode::DRAW_INDEX_MULTI;
        break;
      case 0x68:
        enhancedOpcode = EnhancedPM4Opcode::SET_CONFIG_REG;
        break;
      case 0x69:
        enhancedOpcode = EnhancedPM4Opcode::SET_CONTEXT_REG;
        break;
      case 0x6A:
        enhancedOpcode = EnhancedPM4Opcode::LOAD_CONTEXT_REG;
        break;
      case 0x70:
        enhancedOpcode = EnhancedPM4Opcode::SET_SHADER;
        break;
      case 0x71:
        enhancedOpcode = EnhancedPM4Opcode::LOAD_SHADER;
        break;
      case 0x76:
        enhancedOpcode = EnhancedPM4Opcode::SET_SH_REG;
        break;
      case 0x77:
        enhancedOpcode = EnhancedPM4Opcode::LOAD_SH_REG;
        break;
      case 0x79:
        enhancedOpcode = EnhancedPM4Opcode::SET_UCONFIG_REG;
        break;
      case 0x15:
        enhancedOpcode = EnhancedPM4Opcode::DISPATCH_DIRECT;
        break;
      case 0x16:
        enhancedOpcode = EnhancedPM4Opcode::DISPATCH_INDIRECT;
        break;
      case 0x37:
        enhancedOpcode = EnhancedPM4Opcode::WRITE_DATA;
        break;
      case 0x38:
        enhancedOpcode = EnhancedPM4Opcode::READ_DATA;
        break;
      case 0x40:
        enhancedOpcode = EnhancedPM4Opcode::COPY_DATA;
        break;
      case 0x46:
        enhancedOpcode = EnhancedPM4Opcode::EVENT_WRITE;
        break;
      case 0x47:
        enhancedOpcode = EnhancedPM4Opcode::EVENT_WRITE_EOP;
        break;
      case 0x49:
        enhancedOpcode = EnhancedPM4Opcode::SURFACE_SYNC;
        break;
      case 0x50:
        enhancedOpcode = EnhancedPM4Opcode::DMA_DATA;
        break;
      case 0x58:
        enhancedOpcode = EnhancedPM4Opcode::ACQUIRE_MEM;
        break;
      case 0x59:
        enhancedOpcode = EnhancedPM4Opcode::RELEASE_MEM;
        break;
      case 0x60:
        enhancedOpcode = EnhancedPM4Opcode::SET_SAMPLER;
        break;
      case 0x61:
        enhancedOpcode = EnhancedPM4Opcode::SET_RESOURCE;
        break;
      default:
        enhancedOpcode = EnhancedPM4Opcode::UNKNOWN;
        break;
      }
      break;
    }
    if (ProcessEnhancedPM4Packet(enhancedOpcode, packetData)) {
      offset += header.count();
    } else {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::warn(
          "ProcessType3Packet: Failed to process enhanced opcode: 0x{:02x}",
          static_cast<uint32_t>(enhancedOpcode));
      offset += header.count();
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ProcessType3Packet: opcode=0x{:02x}, latency={}us",
                  static_cast<uint32_t>(header.opcode()), latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessType3Packet failed: {}", e.what());
    throw CommandProcessorException("ProcessType3Packet failed: " +
                                    std::string(e.what()));
  }
}

void CommandProcessor::UpdatePipelineState() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    // Update pipeline state from current register values
    auto &gnmState = m_gpu.GetRegisterState();

    // Update vertex attributes from context registers
    for (uint32_t i = 0; i < 16; ++i) {
      try {
        uint32_t attrReg = gnmState.GetContextRegister(0x200 + i * 4);
        if (attrReg != 0) {
          m_pipelineState.vertexAttributes[i].enabled = true;
          m_pipelineState.vertexAttributes[i].binding = (attrReg >> 16) & 0xF;
          m_pipelineState.vertexAttributes[i].offset = attrReg & 0xFFFF;
          m_pipelineState.vertexAttributes[i].format = (attrReg >> 20) & 0xFF;
          m_pipelineState.vertexAttributes[i].location = i;
          m_pipelineState.activeVertexAttributes |= (1u << i);
        } else {
          m_pipelineState.vertexAttributes[i].enabled = false;
          m_pipelineState.activeVertexAttributes &= ~(1u << i);
        }
      } catch (const std::exception &e) {
        spdlog::trace(
            "UpdatePipelineState: Failed to read vertex attribute {}: {}", i,
            e.what());
      }
    }

    // Update render targets from context registers
    for (uint32_t i = 0; i < 8; ++i) {
      try {
        uint32_t rtReg = gnmState.GetContextRegister(0x100 + i);
        if (rtReg != 0) {
          m_pipelineState.colorTargets[i].enabled = true;
          m_pipelineState.colorTargets[i].surfaceId =
              static_cast<uint64_t>(rtReg) << 8;
          m_pipelineState.colorTargets[i].dirty = true;
          m_pipelineState.activeColorTargets |= (1u << i);
        } else {
          m_pipelineState.colorTargets[i].enabled = false;
          m_pipelineState.activeColorTargets &= ~(1u << i);
        }
      } catch (const std::exception &e) {
        spdlog::trace(
            "UpdatePipelineState: Failed to read render target {}: {}", i,
            e.what());
      }
    }

    // Update depth/stencil state from context registers
    try {
      uint32_t depthReg = gnmState.GetContextRegister(0x80);
      m_pipelineState.depthStencil.depthTestEnable = (depthReg & 0x1) != 0;
      m_pipelineState.depthStencil.depthWriteEnable = (depthReg & 0x2) != 0;
      m_pipelineState.depthStencil.depthCompareOp = (depthReg >> 4) & 0x7;
      m_pipelineState.depthStencil.dirty = true;
    } catch (const std::exception &e) {
      spdlog::trace(
          "UpdatePipelineState: Failed to read depth/stencil state: {}",
          e.what());
    }

    // Update blend state from context registers
    try {
      uint32_t blendReg = gnmState.GetContextRegister(0x90);
      m_pipelineState.blend.targets[0].blendEnable = (blendReg & 0x1) != 0;
      m_pipelineState.blend.targets[0].srcColorBlendFactor =
          (blendReg >> 4) & 0xF;
      m_pipelineState.blend.targets[0].dstColorBlendFactor =
          (blendReg >> 8) & 0xF;
      m_pipelineState.blend.targets[0].colorBlendOp = (blendReg >> 12) & 0x7;
      m_pipelineState.blend.dirty = true;
    } catch (const std::exception &e) {
      spdlog::trace("UpdatePipelineState: Failed to read blend state: {}",
                    e.what());
    }

    // Update shader stages from shader registers
    for (uint32_t stage = 0; stage < 6; ++stage) {
      try {
        uint32_t shaderLo = gnmState.GetShaderRegister(stage, 0x0);
        uint32_t shaderHi = gnmState.GetShaderRegister(stage, 0x1);
        uint64_t shaderId = (static_cast<uint64_t>(shaderHi) << 32) | shaderLo;

        if (shaderId != 0) {
          m_pipelineState.shaderStages[stage].enabled = true;
          m_pipelineState.shaderStages[stage].shaderId = shaderId;
          m_pipelineState.shaderStages[stage].dirty = true;
        } else {
          m_pipelineState.shaderStages[stage].enabled = false;
        }
      } catch (const std::exception &e) {
        spdlog::trace("UpdatePipelineState: Failed to read shader stage {}: {}",
                      stage, e.what());
      }
    }

    // Mark pipeline as dirty and update timestamp
    m_pipelineState.pipelineDirty = true;
    m_pipelineState.lastUpdate = std::chrono::steady_clock::now();
    m_pipelineState.stateHash = ComputeStateHash();

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::trace("UpdatePipelineState: Updated pipeline state, hash=0x{:016x}",
                  m_pipelineState.stateHash);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("UpdatePipelineState failed: {}", e.what());
  }
}

void CommandProcessor::ValidateDrawState() {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    // Validate vertex buffer bindings match vertex attributes
    for (uint32_t i = 0; i < 16; ++i) {
      if (m_pipelineState.vertexAttributes[i].enabled) {
        uint32_t binding = m_pipelineState.vertexAttributes[i].binding;
        if (binding >= 16 || !m_pipelineState.vertexBuffers[binding].enabled) {
          spdlog::warn("ValidateDrawState: Vertex attribute {} references "
                       "unbound buffer {}",
                       i, binding);
          m_stats.errorCount++;
        }
      }
    }

    // Validate at least one render target is bound for color output
    if (m_pipelineState.activeColorTargets == 0 &&
        !m_pipelineState.depthTarget.enabled) {
      spdlog::warn("ValidateDrawState: No render targets bound");
      m_stats.errorCount++;
    }

    // Validate vertex shader is bound
    if (!m_pipelineState.shaderStages[0].enabled) {
      spdlog::warn("ValidateDrawState: No vertex shader bound");
      m_stats.errorCount++;
    }

    // Validate index buffer is bound for indexed draws
    if (!m_pipelineState.indexBuffer.enabled) {
      spdlog::trace("ValidateDrawState: No index buffer bound (non-indexed "
                    "draw assumed)");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::trace("ValidateDrawState: Validation completed");
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ValidateDrawState failed: {}", e.what());
  }
}

void CommandProcessor::TrackCommandDependencies(
    const CommandExecutionContext &context) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    // Add command to execution queue
    m_commandQueue.push_back(context);

    // Track resource dependencies
    for (const auto &dep : context.dependencies) {
      if (dep.type == CommandDependency::MEMORY_READ ||
          dep.type == CommandDependency::MEMORY_WRITE) {
        m_resourceDependencies[dep.address] = context.commandId;
      } else if (dep.type == CommandDependency::RENDER_TARGET ||
                 dep.type == CommandDependency::SHADER_RESOURCE) {
        m_resourceDependencies[dep.resourceId] = context.commandId;
      }
    }

    // Clean up old commands (keep last 1000 for dependency tracking)
    if (m_commandQueue.size() > 1000) {
      m_commandQueue.erase(m_commandQueue.begin(),
                           m_commandQueue.begin() +
                               (m_commandQueue.size() - 1000));
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::trace(
        "TrackCommandDependencies: Tracked command {} with {} dependencies",
        context.commandId, context.dependencies.size());
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("TrackCommandDependencies failed: {}", e.what());
  }
}

uint64_t CommandProcessor::ComputeStateHash() const {
  // Simple hash combining key pipeline state elements
  uint64_t hash = 0;

  // Hash active vertex attributes and buffers
  hash ^= static_cast<uint64_t>(m_pipelineState.activeVertexAttributes) << 32;
  hash ^= static_cast<uint64_t>(m_pipelineState.activeVertexBuffers);

  // Hash active render targets
  hash ^= static_cast<uint64_t>(m_pipelineState.activeColorTargets) << 16;

  // Hash shader stages
  for (uint32_t i = 0; i < 6; ++i) {
    if (m_pipelineState.shaderStages[i].enabled) {
      hash ^= m_pipelineState.shaderStages[i].shaderId + i;
    }
  }

  // Hash primitive topology
  hash ^= static_cast<uint64_t>(m_pipelineState.primitiveTopology) << 8;

  return hash;
}

bool CommandProcessor::IsStateCompatible(uint64_t hash) const {
  return m_pipelineState.stateHash == hash;
}

void CommandProcessor::SetupVertexBufferBindings() {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    auto &gnmState = m_gpu.GetRegisterState();

    // Update vertex buffer bindings from context registers
    for (uint32_t i = 0; i < 16; ++i) {
      try {
        // Read vertex buffer address and stride from context registers
        uint32_t vbAddrLo = gnmState.GetContextRegister(0x300 + i * 4);
        uint32_t vbAddrHi = gnmState.GetContextRegister(0x300 + i * 4 + 1);
        uint32_t vbStride = gnmState.GetContextRegister(0x300 + i * 4 + 2);
        uint32_t vbSize = gnmState.GetContextRegister(0x300 + i * 4 + 3);

        uint64_t vbAddress = (static_cast<uint64_t>(vbAddrHi) << 32) | vbAddrLo;

        if (vbAddress != 0) {
          // Validate vertex buffer memory access
          if (ValidateMemoryAccess(vbAddress, vbSize, false)) {
            // Check if this is tiled memory and translate if necessary
            uint64_t linearAddress = vbAddress;
            if (IsTiledMemoryAddress(vbAddress)) {
              linearAddress = TranslateTiledAddress(
                  vbAddress, 1); // Assume 1D tiling for vertex buffers
              spdlog::trace("SetupVertexBufferBindings: Translated tiled VB{} "
                            "address 0x{:016x} to 0x{:016x}",
                            i, vbAddress, linearAddress);
            }

            m_pipelineState.vertexBuffers[i].enabled = true;
            m_pipelineState.vertexBuffers[i].address = vbAddress;
            m_pipelineState.vertexBuffers[i].stride = vbStride;
            m_pipelineState.vertexBuffers[i].size = vbSize;
            m_pipelineState.activeVertexBuffers |= (1u << i);

            // Bind vertex buffer to GPU using linear address
            m_gpu.BindVertexBuffer(i, linearAddress, vbStride);
          } else {
            spdlog::warn("SetupVertexBufferBindings: Memory validation failed "
                         "for vertex buffer {} at 0x{:016x}",
                         i, vbAddress);
            m_pipelineState.vertexBuffers[i].enabled = false;
            m_pipelineState.activeVertexBuffers &= ~(1u << i);
          }
        } else {
          m_pipelineState.vertexBuffers[i].enabled = false;
          m_pipelineState.activeVertexBuffers &= ~(1u << i);
        }
      } catch (const std::exception &e) {
        spdlog::trace(
            "SetupVertexBufferBindings: Failed to setup vertex buffer {}: {}",
            i, e.what());
      }
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::trace("SetupVertexBufferBindings: Setup {} vertex buffers",
                  __builtin_popcount(m_pipelineState.activeVertexBuffers));
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SetupVertexBufferBindings failed: {}", e.what());
  }
}

void CommandProcessor::ValidateIndexBuffer(uint64_t address,
                                           uint32_t indexCount) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    if (address == 0) {
      spdlog::warn("ValidateIndexBuffer: Index buffer address is null");
      m_stats.errorCount++;
      return;
    }

    // Estimate index buffer size (assume 32-bit indices for now)
    uint32_t estimatedSize = indexCount * sizeof(uint32_t);

    // Validate memory access using enhanced validation
    if (!ValidateMemoryAccess(address, estimatedSize, false)) {
      spdlog::error("ValidateIndexBuffer: Memory validation failed for index "
                    "buffer at 0x{:016x}",
                    address);
      m_stats.errorCount++;
      throw CommandProcessorException("Index buffer memory validation failed");
    }

    // Check if this is tiled memory and translate if necessary
    uint64_t linearAddress = address;
    if (IsTiledMemoryAddress(address)) {
      linearAddress =
          TranslateTiledAddress(address, 0); // Assume linear for index buffers
      spdlog::trace("ValidateIndexBuffer: Translated tiled address 0x{:016x} "
                    "to 0x{:016x}",
                    address, linearAddress);
    }

    // Update pipeline state
    m_pipelineState.indexBuffer.address = address;
    m_pipelineState.indexBuffer.size = estimatedSize;
    m_pipelineState.indexBuffer.format = 1; // 32-bit indices
    m_pipelineState.indexBuffer.enabled = true;
    m_pipelineState.indexBuffer.dirty = true;

    // Bind index buffer to GPU
    m_gpu.BindIndexBuffer(address, VK_INDEX_TYPE_UINT32);

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::trace(
        "ValidateIndexBuffer: Validated index buffer at 0x{:016x}, count={}",
        address, indexCount);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ValidateIndexBuffer failed: {}", e.what());
    throw;
  }
}

void CommandProcessor::ProcessMultiDrawCommands(
    const std::vector<uint32_t> &drawData) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    if (drawData.size() % 3 != 0) {
      spdlog::error("ProcessMultiDrawCommands: Invalid draw data size {}",
                    drawData.size());
      m_stats.errorCount++;
      throw CommandProcessorException("Invalid multi-draw data format");
    }

    uint32_t drawCount = drawData.size() / 3;
    spdlog::trace("ProcessMultiDrawCommands: Processing {} draw commands",
                  drawCount);

    // Setup vertex buffer bindings once for all draws
    SetupVertexBufferBindings();

    // Update pipeline state
    UpdatePipelineState();

    // Validate draw state
    ValidateDrawState();

    // Process each draw command
    for (uint32_t i = 0; i < drawCount; ++i) {
      uint32_t indexCount = drawData[i * 3];
      uint32_t instanceCount = drawData[i * 3 + 1];
      uint32_t firstIndex = drawData[i * 3 + 2];

      // Create command execution context for each draw
      CommandExecutionContext context;
      context.commandId = m_nextCommandId++;
      context.timestamp = std::chrono::steady_clock::now();
      context.estimatedCycles = 50; // Estimate for draw command
      context.requiresSync = true;

      // Add dependencies for active vertex buffers
      for (uint32_t j = 0; j < 16; ++j) {
        if (m_pipelineState.vertexBuffers[j].enabled) {
          CommandDependency vertexDep;
          vertexDep.type = CommandDependency::MEMORY_READ;
          vertexDep.address = m_pipelineState.vertexBuffers[j].address;
          vertexDep.size = m_pipelineState.vertexBuffers[j].size;
          vertexDep.isWrite = false;
          context.dependencies.push_back(vertexDep);
        }
      }

      // Add dependencies for active render targets
      for (uint32_t j = 0; j < 8; ++j) {
        if (m_pipelineState.colorTargets[j].enabled) {
          CommandDependency rtDep;
          rtDep.type = CommandDependency::RENDER_TARGET;
          rtDep.resourceId = m_pipelineState.colorTargets[j].surfaceId;
          rtDep.isWrite = true;
          context.dependencies.push_back(rtDep);
        }
      }

      // Track command dependencies
      TrackCommandDependencies(context);

      // Execute the draw command
      m_gpu.DrawIndex(indexCount, instanceCount, firstIndex, 0, 0);
      m_stats.drawCalls++;

      spdlog::trace("ProcessMultiDrawCommands: Draw {}: indices={}, "
                    "instances={}, firstIndex={}",
                    i, indexCount, instanceCount, firstIndex);
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::trace("ProcessMultiDrawCommands: Completed {} draws, latency={}us",
                  drawCount, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessMultiDrawCommands failed: {}", e.what());
    throw CommandProcessorException("ProcessMultiDrawCommands failed: " +
                                    std::string(e.what()));
  }
}

bool CommandProcessor::IsVertexAttributeCompatible(uint32_t binding,
                                                   uint32_t format) {
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    // Check if binding is valid
    if (binding >= 16) {
      return false;
    }

    // Check if vertex buffer is bound
    if (!m_pipelineState.vertexBuffers[binding].enabled) {
      return false;
    }

    // Basic format compatibility check (simplified)
    // In a real implementation, this would check format compatibility
    // between vertex buffer data and shader input expectations
    switch (format) {
    case 0: // R32_FLOAT
    case 1: // RG32_FLOAT
    case 2: // RGB32_FLOAT
    case 3: // RGBA32_FLOAT
    case 4: // R16_UNORM
    case 5: // RG16_UNORM
    case 6: // RGBA16_UNORM
      return true;
    default:
      spdlog::warn("IsVertexAttributeCompatible: Unknown format {}", format);
      return false;
    }
  } catch (const std::exception &e) {
    spdlog::error("IsVertexAttributeCompatible failed: {}", e.what());
    return false;
  }
}

bool CommandProcessor::ValidateContextRegister(uint32_t offset,
                                               uint32_t value) {
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    // Validate render target registers (0x100-0x107)
    if (offset >= 0x100 && offset < 0x108) {
      return ValidateRenderTargetState(offset - 0x100, value);
    }

    // Validate depth/stencil control register (0x80)
    if (offset == 0x80) {
      return ValidateDepthStencilState(offset, value);
    }

    // Validate blend state registers (0x90-0x97)
    if (offset >= 0x90 && offset < 0x98) {
      return ValidateBlendState(offset, value);
    }

    // Validate viewport registers (0x60-0x67)
    if (offset >= 0x60 && offset < 0x68) {
      // Viewport coordinates should be reasonable
      if (offset == 0x60 || offset == 0x61) { // X, Y coordinates
        if (value > 8192) { // Reasonable maximum viewport size
          spdlog::warn(
              "ValidateContextRegister: Viewport coordinate {} too large: {}",
              offset, value);
          return false;
        }
      } else if (offset == 0x62 || offset == 0x63) { // Width, Height
        if (value == 0 || value > 8192) {
          spdlog::warn("ValidateContextRegister: Invalid viewport dimension {} "
                       "for offset {}",
                       value, offset);
          return false;
        }
      }
    }

    // Validate vertex attribute registers (0x200-0x23F)
    if (offset >= 0x200 && offset < 0x240) {
      uint32_t attrIndex = (offset - 0x200) / 4;
      if (attrIndex < 16) {
        uint32_t binding = (value >> 16) & 0xF;
        uint32_t format = (value >> 20) & 0xFF;

        if (binding >= 16) {
          spdlog::warn("ValidateContextRegister: Invalid vertex buffer binding "
                       "{} for attribute {}",
                       binding, attrIndex);
          return false;
        }

        if (!IsVertexAttributeCompatible(binding, format)) {
          spdlog::warn("ValidateContextRegister: Incompatible vertex attribute "
                       "format {} for binding {}",
                       format, binding);
          return false;
        }
      }
    }

    return true; // Default to valid for unknown registers
  } catch (const std::exception &e) {
    spdlog::error("ValidateContextRegister failed: {}", e.what());
    return false;
  }
}

bool CommandProcessor::ValidateShaderRegister(uint32_t stage, uint32_t offset,
                                              uint32_t value) {
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    // Validate shader stage
    if (stage >= 6) {
      spdlog::warn("ValidateShaderRegister: Invalid shader stage {}", stage);
      return false;
    }

    // Validate shader address registers (0x0, 0x1)
    if (offset == 0x0 || offset == 0x1) {
      // Shader addresses should be aligned and within valid memory ranges
      if (offset == 0x0 && (value & 0x3) != 0) {
        spdlog::warn("ValidateShaderRegister: Shader address low part not "
                     "aligned: 0x{:08x}",
                     value);
        return false;
      }
    }

    // Validate shader resource registers (0x10-0x1F)
    if (offset >= 0x10 && offset < 0x20) {
      // Resource binding validation would go here
      // For now, just check for reasonable values
      if (value != 0 && (value & 0xF) >= 16) {
        spdlog::warn("ValidateShaderRegister: Invalid resource binding in "
                     "register 0x{:04x}: 0x{:08x}",
                     offset, value);
        return false;
      }
    }

    // Validate shader constant buffer registers (0x20-0x2F)
    if (offset >= 0x20 && offset < 0x30) {
      // Constant buffer addresses should be aligned
      if (value != 0 && (value & 0xF) != 0) {
        spdlog::warn("ValidateShaderRegister: Constant buffer address not "
                     "aligned: 0x{:08x}",
                     value);
        return false;
      }
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidateShaderRegister failed: {}", e.what());
    return false;
  }
}

bool CommandProcessor::ValidateRenderTargetState(uint32_t index,
                                                 uint32_t value) {
  try {
    if (index >= 8) {
      spdlog::warn("ValidateRenderTargetState: Invalid render target index {}",
                   index);
      return false;
    }

    if (value != 0) {
      // Extract surface ID and validate
      uint64_t surfaceId = static_cast<uint64_t>(value) << 8;

      // Check if surface ID is reasonable (non-zero, aligned)
      if (surfaceId == 0) {
        spdlog::warn("ValidateRenderTargetState: Invalid surface ID for render "
                     "target {}",
                     index);
        return false;
      }

      // In a real implementation, we would validate the surface exists and has
      // compatible format
      spdlog::trace("ValidateRenderTargetState: Render target {} bound to "
                    "surface 0x{:016x}",
                    index, surfaceId);
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidateRenderTargetState failed: {}", e.what());
    return false;
  }
}

bool CommandProcessor::ValidateBlendState(uint32_t offset, uint32_t value) {
  try {
    // Validate blend operation values
    if (offset == 0x90) { // Main blend control register
      uint32_t srcBlend = (value >> 4) & 0xF;
      uint32_t dstBlend = (value >> 8) & 0xF;
      uint32_t blendOp = (value >> 12) & 0x7;

      // Validate blend factors (0-15 are valid PS4 blend factors)
      if (srcBlend > 15 || dstBlend > 15) {
        spdlog::warn("ValidateBlendState: Invalid blend factors src={}, dst={}",
                     srcBlend, dstBlend);
        return false;
      }

      // Validate blend operations (0-7 are valid PS4 blend operations)
      if (blendOp > 7) {
        spdlog::warn("ValidateBlendState: Invalid blend operation {}", blendOp);
        return false;
      }
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidateBlendState failed: {}", e.what());
    return false;
  }
}

bool CommandProcessor::ValidateDepthStencilState(uint32_t offset,
                                                 uint32_t value) {
  try {
    if (offset == 0x80) { // Depth/stencil control register
      uint32_t depthFunc = (value >> 4) & 0x7;
      uint32_t stencilFunc = (value >> 8) & 0x7;

      // Validate depth comparison functions (0-7 are valid)
      if (depthFunc > 7) {
        spdlog::warn("ValidateDepthStencilState: Invalid depth function {}",
                     depthFunc);
        return false;
      }

      // Validate stencil comparison functions (0-7 are valid)
      if (stencilFunc > 7) {
        spdlog::warn("ValidateDepthStencilState: Invalid stencil function {}",
                     stencilFunc);
        return false;
      }
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidateDepthStencilState failed: {}", e.what());
    return false;
  }
}

void CommandProcessor::ApplyRegisterConstraints(uint32_t &value,
                                                uint32_t offset,
                                                bool isShaderReg) {
  try {
    if (isShaderReg) {
      // Apply shader register constraints
      if (offset == 0x0) {
        // Ensure shader address low part is aligned to 4 bytes
        value &= ~0x3;
      } else if (offset >= 0x20 && offset < 0x30) {
        // Ensure constant buffer addresses are aligned to 16 bytes
        value &= ~0xF;
      }
    } else {
      // Apply context register constraints
      if (offset >= 0x60 && offset < 0x68) {
        // Clamp viewport values to reasonable ranges
        if (offset == 0x60 || offset == 0x61) { // X, Y coordinates
          value = std::min(value, 8192u);
        } else if (offset == 0x62 || offset == 0x63) { // Width, Height
          value = std::max(1u, std::min(value, 8192u));
        }
      } else if (offset >= 0x90 && offset < 0x98) {
        // Clamp blend state values
        if (offset == 0x90) {
          uint32_t srcBlend = (value >> 4) & 0xF;
          uint32_t dstBlend = (value >> 8) & 0xF;
          uint32_t blendOp = (value >> 12) & 0x7;

          srcBlend = std::min(srcBlend, 15u);
          dstBlend = std::min(dstBlend, 15u);
          blendOp = std::min(blendOp, 7u);

          value = (value & 0xFFFF0000) | (blendOp << 12) | (dstBlend << 8) |
                  (srcBlend << 4) | (value & 0xF);
        }
      }
    }

    spdlog::trace("ApplyRegisterConstraints: Applied constraints to register "
                  "0x{:04x}, value=0x{:08x}",
                  offset, value);
  } catch (const std::exception &e) {
    spdlog::error("ApplyRegisterConstraints failed: {}", e.what());
  }
}

bool CommandProcessor::ValidateMemoryAccess(uint64_t address, uint32_t size,
                                            bool isWrite) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    if (address == 0) {
      spdlog::warn("ValidateMemoryAccess: Null address access");
      return false;
    }

    if (size == 0) {
      spdlog::warn("ValidateMemoryAccess: Zero size access");
      return false;
    }

    // Check for address overflow
    if (address + size < address) {
      spdlog::warn("ValidateMemoryAccess: Address overflow: 0x{:016x} + {}",
                   address, size);
      return false;
    }

    // Test memory accessibility with MMU
    uint8_t testByte;
    if (!m_mmu.ReadVirtual(address, &testByte, 1, 0)) {
      spdlog::warn("ValidateMemoryAccess: Cannot read from address 0x{:016x}",
                   address);
      return false;
    }

    // For write operations, test write accessibility
    if (isWrite) {
      // In a real implementation, we would check write permissions
      // For now, assume all readable memory is writable
      spdlog::trace("ValidateMemoryAccess: Write access to 0x{:016x} size {}",
                    address, size);
    }

    // Track memory dependency
    TrackMemoryDependency(address, size, isWrite);

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::trace(
        "ValidateMemoryAccess: Validated {} access to 0x{:016x} size {}",
        isWrite ? "write" : "read", address, size);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ValidateMemoryAccess failed: {}", e.what());
    return false;
  }
}

bool CommandProcessor::ValidateResourceBinding(uint64_t resourceId,
                                               uint32_t binding) {
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    if (resourceId == 0) {
      spdlog::trace("ValidateResourceBinding: Unbinding resource from slot {}",
                    binding);
      return true; // Unbinding is always valid
    }

    if (binding >=
        32) { // PS4 typically supports up to 32 resource bindings per stage
      spdlog::warn("ValidateResourceBinding: Invalid binding slot {}", binding);
      return false;
    }

    // In a real implementation, we would validate that the resource exists
    // and is compatible with the binding slot type
    spdlog::trace(
        "ValidateResourceBinding: Binding resource 0x{:016x} to slot {}",
        resourceId, binding);

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidateResourceBinding failed: {}", e.what());
    return false;
  }
}

void CommandProcessor::TrackMemoryDependency(uint64_t address, uint32_t size,
                                             bool isWrite) {
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    // Create a memory dependency entry
    CommandDependency memDep;
    memDep.type = isWrite ? CommandDependency::MEMORY_WRITE
                          : CommandDependency::MEMORY_READ;
    memDep.address = address;
    memDep.size = size;
    memDep.isWrite = isWrite;
    memDep.resourceId = 0; // Not applicable for memory dependencies

    // Add to current command context if one exists
    if (!m_commandQueue.empty()) {
      m_commandQueue.back().dependencies.push_back(memDep);
    }

    // Track in resource dependencies map
    m_resourceDependencies[address] = m_nextCommandId - 1;

    spdlog::trace(
        "TrackMemoryDependency: Tracked {} dependency at 0x{:016x} size {}",
        isWrite ? "write" : "read", address, size);
  } catch (const std::exception &e) {
    spdlog::error("TrackMemoryDependency failed: {}", e.what());
  }
}

bool CommandProcessor::IsTiledMemoryAddress(uint64_t address) {
  try {
    // PS4 uses specific address ranges for tiled memory
    // This is a simplified check - real PS4 has complex tiling rules

    // Check if address is in GPU memory range (simplified)
    if (address >= 0x100000000ULL && address < 0x200000000ULL) {
      // Check alignment - tiled memory typically has specific alignment
      // requirements
      if ((address & 0xFFF) == 0) { // 4KB aligned
        spdlog::trace(
            "IsTiledMemoryAddress: Address 0x{:016x} appears to be tiled",
            address);
        return true;
      }
    }

    return false;
  } catch (const std::exception &e) {
    spdlog::error("IsTiledMemoryAddress failed: {}", e.what());
    return false;
  }
}

uint64_t CommandProcessor::TranslateTiledAddress(uint64_t tiledAddress,
                                                 uint32_t tileMode) {
  try {
    // This is a simplified tiled address translation
    // Real PS4 has complex tiling algorithms based on tile mode

    if (!IsTiledMemoryAddress(tiledAddress)) {
      // Not a tiled address, return as-is
      return tiledAddress;
    }

    // Simplified translation based on tile mode
    uint64_t linearAddress = tiledAddress;

    switch (tileMode) {
    case 0: // Linear (no tiling)
      break;
    case 1: // 1D tiling
      // Simple 1D tiling transformation
      linearAddress =
          (tiledAddress & ~0xFFF) | ((tiledAddress & 0xFFF) ^ 0x800);
      break;
    case 2: // 2D tiling
      // Simple 2D tiling transformation (Morton order approximation)
      {
        uint64_t x = (tiledAddress >> 2) & 0x3FF;
        uint64_t y = (tiledAddress >> 12) & 0x3FF;
        uint64_t base = tiledAddress & ~0x3FFFFF;

        // Simplified Morton encoding
        uint64_t morton = 0;
        for (int i = 0; i < 10; i++) {
          morton |= ((x & (1ULL << i)) << i) | ((y & (1ULL << i)) << (i + 1));
        }

        linearAddress = base | (morton << 2);
      }
      break;
    default:
      spdlog::warn("TranslateTiledAddress: Unknown tile mode {}", tileMode);
      break;
    }

    spdlog::trace(
        "TranslateTiledAddress: Translated 0x{:016x} (mode {}) to 0x{:016x}",
        tiledAddress, tileMode, linearAddress);

    return linearAddress;
  } catch (const std::exception &e) {
    spdlog::error("TranslateTiledAddress failed: {}", e.what());
    return tiledAddress; // Return original address on error
  }
}

void CommandProcessor::OptimizeCommandSequence() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    if (m_pendingCommands.size() < 2) {
      return; // Nothing to optimize
    }

    // Eliminate redundant state changes
    EliminateRedundantState();

    // Batch compatible commands
    std::vector<CommandExecutionContext> optimizedCommands;
    optimizedCommands.reserve(m_pendingCommands.size());

    for (size_t i = 0; i < m_pendingCommands.size(); ++i) {
      bool batched = false;

      // Try to batch with previous command
      if (!optimizedCommands.empty()) {
        auto &lastCmd = optimizedCommands.back();
        if (CanBatchCommands(lastCmd, m_pendingCommands[i])) {
          // Merge dependencies
          lastCmd.dependencies.insert(lastCmd.dependencies.end(),
                                      m_pendingCommands[i].dependencies.begin(),
                                      m_pendingCommands[i].dependencies.end());
          lastCmd.estimatedCycles += m_pendingCommands[i].estimatedCycles;
          batched = true;

          spdlog::trace(
              "OptimizeCommandSequence: Batched command {} with previous",
              m_pendingCommands[i].commandId);
        }
      }

      if (!batched) {
        optimizedCommands.push_back(m_pendingCommands[i]);
      }
    }

    // Replace pending commands with optimized sequence
    m_pendingCommands = std::move(optimizedCommands);

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::trace("OptimizeCommandSequence: Optimized {} commands",
                  m_pendingCommands.size());
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("OptimizeCommandSequence failed: {}", e.what());
  }
}

bool CommandProcessor::CanBatchCommands(const CommandExecutionContext &cmd1,
                                        const CommandExecutionContext &cmd2) {
  try {
    // Commands can be batched if they don't have conflicting dependencies
    for (const auto &dep1 : cmd1.dependencies) {
      for (const auto &dep2 : cmd2.dependencies) {
        // Check for write-after-write or read-after-write conflicts
        if (dep1.address == dep2.address && (dep1.isWrite || dep2.isWrite)) {
          if (dep1.isWrite && dep2.isWrite) {
            return false; // Write-after-write conflict
          }
          if (dep1.isWrite && !dep2.isWrite) {
            return false; // Read-after-write conflict
          }
          if (!dep1.isWrite && dep2.isWrite) {
            return false; // Write-after-read conflict
          }
        }

        // Check for resource conflicts
        if (dep1.type == CommandDependency::RENDER_TARGET &&
            dep2.type == CommandDependency::RENDER_TARGET &&
            dep1.resourceId == dep2.resourceId) {
          return false; // Render target conflict
        }
      }
    }

    // Commands can be batched if they don't require synchronization
    if (cmd1.requiresSync || cmd2.requiresSync) {
      return false;
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("CanBatchCommands failed: {}", e.what());
    return false;
  }
}

void CommandProcessor::EliminateRedundantState() {
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    // Remove consecutive state changes that set the same state
    auto it = m_pendingCommands.begin();
    while (it != m_pendingCommands.end()) {
      bool isRedundant = false;

      // Check if this is a pipeline state command
      for (const auto &dep : it->dependencies) {
        if (dep.type == CommandDependency::PIPELINE_STATE) {
          // Check if we already applied this state
          uint64_t currentStateHash = ComputeStateHash();
          if (currentStateHash == m_lastStateHash) {
            isRedundant = true;
            spdlog::trace(
                "EliminateRedundantState: Removing redundant state command {}",
                it->commandId);
            break;
          }
        }
      }

      if (isRedundant) {
        it = m_pendingCommands.erase(it);
      } else {
        ++it;
      }
    }

    spdlog::trace("EliminateRedundantState: Processed {} commands",
                  m_pendingCommands.size());
  } catch (const std::exception &e) {
    spdlog::error("EliminateRedundantState failed: {}", e.what());
  }
}

void CommandProcessor::FlushPendingCommands() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    if (m_pendingCommands.empty()) {
      return;
    }

    // Optimize the command sequence before flushing
    OptimizeCommandSequence();

    // Execute all pending commands
    for (const auto &cmd : m_pendingCommands) {
      // Add to main command queue
      m_commandQueue.push_back(cmd);

      // Update last state hash for redundancy elimination
      for (const auto &dep : cmd.dependencies) {
        if (dep.type == CommandDependency::PIPELINE_STATE) {
          m_lastStateHash = ComputeStateHash();
          m_cachedPipelineStates.insert(m_lastStateHash);
          break;
        }
      }
    }

    // Clear pending commands
    m_pendingCommands.clear();

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::trace("FlushPendingCommands: Flushed {} commands",
                  m_commandQueue.size());
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("FlushPendingCommands failed: {}", e.what());
  }
}

bool CommandProcessor::IsPipelineStateCached(uint64_t stateHash) {
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    bool cached =
        m_cachedPipelineStates.find(stateHash) != m_cachedPipelineStates.end();

    if (cached) {
      m_stats.cacheHits++;
      spdlog::trace(
          "IsPipelineStateCached: State hash 0x{:016x} found in cache",
          stateHash);
    } else {
      m_stats.cacheMisses++;
      spdlog::trace("IsPipelineStateCached: State hash 0x{:016x} not cached",
                    stateHash);
    }

    return cached;
  } catch (const std::exception &e) {
    spdlog::error("IsPipelineStateCached failed: {}", e.what());
    return false;
  }
}

void CommandProcessor::ValidateCommandBuffer(uint64_t address, uint32_t size) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    if (address == 0 || size == 0) {
      spdlog::error("ValidateCommandBuffer: Invalid command buffer "
                    "address=0x{:016x} size={}",
                    address, size);
      m_stats.errorCount++;
      throw CommandProcessorException("Invalid command buffer parameters");
    }

    // Check if command buffer is accessible
    if (!ValidateMemoryAccess(address, size, false)) {
      spdlog::error(
          "ValidateCommandBuffer: Command buffer memory validation failed");
      m_stats.errorCount++;
      throw CommandProcessorException("Command buffer not accessible");
    }

    // Basic integrity checks
    uint64_t currentAddr = address;
    uint64_t endAddr = address + size;
    uint32_t packetCount = 0;

    while (currentAddr < endAddr) {
      try {
        // Read packet header
        PM4Header header;
        if (!m_mmu.ReadVirtual(currentAddr, &header, sizeof(header), 0)) {
          spdlog::error(
              "ValidateCommandBuffer: Cannot read packet header at 0x{:016x}",
              currentAddr);
          m_stats.errorCount++;
          break;
        }

        // Validate packet header
        if (header.count() == 0 || header.count() > 0x3FFF) {
          spdlog::warn(
              "ValidateCommandBuffer: Suspicious packet count {} at 0x{:016x}",
              header.count(), currentAddr);
        }

        // Check if opcode is known
        if (static_cast<uint32_t>(header.opcode()) > 0xFF) {
          spdlog::warn(
              "ValidateCommandBuffer: Unknown opcode 0x{:02x} at 0x{:016x}",
              static_cast<uint32_t>(header.opcode()), currentAddr);
        }

        // Advance to next packet
        uint32_t packetSize = (header.count() + 1) * sizeof(uint32_t);
        currentAddr += packetSize;
        packetCount++;

        // Prevent infinite loops
        if (packetCount > 10000) {
          spdlog::warn("ValidateCommandBuffer: Too many packets ({}), stopping "
                       "validation",
                       packetCount);
          break;
        }

      } catch (const std::exception &e) {
        spdlog::error(
            "ValidateCommandBuffer: Error validating packet at 0x{:016x}: {}",
            currentAddr, e.what());
        m_stats.errorCount++;
        break;
      }
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::info("ValidateCommandBuffer: Validated {} packets in command "
                 "buffer at 0x{:016x}",
                 packetCount, address);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ValidateCommandBuffer failed: {}", e.what());
    throw;
  }
}

void CommandProcessor::DumpPipelineState(std::ostream &out) {
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    out << "=== Pipeline State Dump ===\n";
    out << "State Hash: 0x" << std::hex << m_pipelineState.stateHash << std::dec
        << "\n";
    out << "Pipeline Dirty: "
        << (m_pipelineState.pipelineDirty ? "true" : "false") << "\n";
    out << "Render Pass Dirty: "
        << (m_pipelineState.renderPassDirty ? "true" : "false") << "\n";

    // Vertex attributes
    out << "\nVertex Attributes (active mask: 0x" << std::hex
        << m_pipelineState.activeVertexAttributes << std::dec << "):\n";
    for (uint32_t i = 0; i < 16; ++i) {
      if (m_pipelineState.vertexAttributes[i].enabled) {
        const auto &attr = m_pipelineState.vertexAttributes[i];
        out << "  [" << i << "] binding=" << attr.binding
            << " offset=" << attr.offset << " format=" << attr.format
            << " location=" << attr.location << "\n";
      }
    }

    // Vertex buffers
    out << "\nVertex Buffers (active mask: 0x" << std::hex
        << m_pipelineState.activeVertexBuffers << std::dec << "):\n";
    for (uint32_t i = 0; i < 16; ++i) {
      if (m_pipelineState.vertexBuffers[i].enabled) {
        const auto &vb = m_pipelineState.vertexBuffers[i];
        out << "  [" << i << "] address=0x" << std::hex << vb.address
            << std::dec << " stride=" << vb.stride << " size=" << vb.size
            << "\n";
      }
    }

    // Index buffer
    if (m_pipelineState.indexBuffer.enabled) {
      out << "\nIndex Buffer:\n";
      out << "  address=0x" << std::hex << m_pipelineState.indexBuffer.address
          << std::dec << " size=" << m_pipelineState.indexBuffer.size
          << " format=" << m_pipelineState.indexBuffer.format << "\n";
    }

    // Shader stages
    out << "\nShader Stages:\n";
    const char *stageNames[] = {"VS", "HS", "DS", "GS", "PS", "CS"};
    for (uint32_t i = 0; i < 6; ++i) {
      if (m_pipelineState.shaderStages[i].enabled) {
        const auto &shader = m_pipelineState.shaderStages[i];
        out << "  " << stageNames[i] << ": id=0x" << std::hex << shader.shaderId
            << std::dec << " code=0x" << std::hex << shader.codeAddress
            << std::dec << " size=" << shader.codeSize << "\n";
      }
    }

    // Render targets
    out << "\nRender Targets (active mask: 0x" << std::hex
        << m_pipelineState.activeColorTargets << std::dec << "):\n";
    for (uint32_t i = 0; i < 8; ++i) {
      if (m_pipelineState.colorTargets[i].enabled) {
        const auto &rt = m_pipelineState.colorTargets[i];
        out << "  [" << i << "] surface=0x" << std::hex << rt.surfaceId
            << std::dec << " format=" << rt.format << " " << rt.width << "x"
            << rt.height << " pitch=" << rt.pitch << "\n";
      }
    }

    out << "\n=== End Pipeline State ===\n";
  } catch (const std::exception &e) {
    out << "Error dumping pipeline state: " << e.what() << "\n";
    spdlog::error("DumpPipelineState failed: {}", e.what());
  }
}

void CommandProcessor::DumpCommandQueue(std::ostream &out) {
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    out << "=== Command Queue Dump ===\n";
    out << "Queue Size: " << m_commandQueue.size() << "\n";
    out << "Pending Commands: " << m_pendingCommands.size() << "\n";
    out << "Next Command ID: " << m_nextCommandId << "\n";

    out << "\nActive Commands:\n";
    for (size_t i = 0; i < std::min(m_commandQueue.size(), size_t(10)); ++i) {
      const auto &cmd = m_commandQueue[i];
      out << "  [" << i << "] ID=" << cmd.commandId
          << " cycles=" << cmd.estimatedCycles
          << " sync=" << (cmd.requiresSync ? "true" : "false")
          << " deps=" << cmd.dependencies.size() << "\n";

      // Show dependencies
      for (size_t j = 0; j < std::min(cmd.dependencies.size(), size_t(3));
           ++j) {
        const auto &dep = cmd.dependencies[j];
        const char *typeNames[] = {"MEM_READ", "MEM_WRITE", "RT", "SHADER_RES",
                                   "PIPELINE"};
        out << "    dep[" << j << "] " << typeNames[dep.type];
        if (dep.type <= CommandDependency::MEMORY_WRITE) {
          out << " addr=0x" << std::hex << dep.address << std::dec
              << " size=" << dep.size;
        } else {
          out << " res=0x" << std::hex << dep.resourceId << std::dec;
        }
        out << " write=" << (dep.isWrite ? "true" : "false") << "\n";
      }
      if (cmd.dependencies.size() > 3) {
        out << "    ... and " << (cmd.dependencies.size() - 3)
            << " more dependencies\n";
      }
    }

    if (m_commandQueue.size() > 10) {
      out << "  ... and " << (m_commandQueue.size() - 10) << " more commands\n";
    }

    out << "\n=== End Command Queue ===\n";
  } catch (const std::exception &e) {
    out << "Error dumping command queue: " << e.what() << "\n";
    spdlog::error("DumpCommandQueue failed: {}", e.what());
  }
}

void CommandProcessor::EnableDebugMode(bool enable) {
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    m_debugMode = enable;
    spdlog::info("CommandProcessor debug mode {}",
                 enable ? "enabled" : "disabled");
  } catch (const std::exception &e) {
    spdlog::error("EnableDebugMode failed: {}", e.what());
  }
}

void CommandProcessor::SetDebugBreakpoint(uint32_t opcode) {
  std::unique_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    m_debugBreakpoints.insert(opcode);
    spdlog::info("CommandProcessor debug breakpoint set on opcode 0x{:02x}",
                 opcode);
  } catch (const std::exception &e) {
    spdlog::error("SetDebugBreakpoint failed: {}", e.what());
  }
}

bool CommandProcessor::CheckDebugBreakpoint(uint32_t opcode) {
  std::shared_lock<std::shared_mutex> lock(m_processorMutex);
  try {
    if (!m_debugMode) {
      return false;
    }

    bool hit = m_debugBreakpoints.find(opcode) != m_debugBreakpoints.end();
    if (hit) {
      spdlog::warn("CommandProcessor debug breakpoint hit on opcode 0x{:02x}",
                   opcode);
    }

    return hit;
  } catch (const std::exception &e) {
    spdlog::error("CheckDebugBreakpoint failed: {}", e.what());
    return false;
  }
}

} // namespace ps4